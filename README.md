# Investment Toolkit - Frontend/Backend Architecture

A comprehensive financial analysis platform split into separate frontend and backend applications for better scalability and maintainability.

## Architecture Overview

### Backend (Spring Boot API Server)
- **Technology**: Java 21, Spring Boot 3.2.3, DuckDB
- **Port**: 8080
- **Context Path**: `/investment-toolkit`
- **API Base**: `http://localhost:8080/investment-toolkit/api`

### Frontend (React Application)
- **Technology**: React 18, TypeScript, Material-UI
- **Port**: 3000 (development)
- **URL**: `http://localhost:3000`

## Quick Start

### 1. Start the Backend
```bash
# From project root
./gradlew bootRun
```
Backend will be available at: `http://localhost:8080/investment-toolkit`

### 2. Start the Frontend
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies (first time only)
npm install

# Start development server
npm start
```
Frontend will be available at: `http://localhost:3000`

## Project Structure

```
InvestmentTookKitV2/
├── src/main/java/com/investment/          # Backend Spring Boot application
│   ├── api/controller/                    # REST API controllers
│   ├── config/                           # Configuration classes (including CORS)
│   ├── service/                          # Business logic services
│   ├── model/                            # Domain models
│   └── database/                         # Database management
├── frontend/                             # React frontend application
│   ├── src/
│   │   ├── components/                   # Reusable React components
│   │   ├── pages/                        # Page components
│   │   ├── services/api/                 # API service layer
│   │   └── types/                        # TypeScript interfaces
│   ├── public/                           # Static assets
│   └── package.json                      # Frontend dependencies
├── data/                                 # DuckDB database files
├── logs/                                 # Application logs
├── build.gradle                          # Backend build configuration
└── README.md                            # This file
```

## Features

### Backend API Endpoints
- **Instruments** (`/api/instruments`) - Financial instrument management
- **OHLCV** (`/api/ohlcv`) - Price data operations
- **Technical Indicators** (`/api/technical-indicators`) - Bollinger Bands, DMI
- **Positions** (`/api/positions`) - Portfolio management
- **Watch List** (`/api/watchlist`) - Symbol tracking
- **Processes** (`/api/processes`) - Background operation monitoring

### Frontend Pages
- **Dashboard** - System overview and statistics
- **Instruments** - Search and manage financial instruments
- **OHLCV Data** - Interactive price charts with technical indicators
- **Technical Indicators** - Calculate Bollinger Bands and DMI
- **Positions** - Portfolio position management
- **Watch List** - Symbol tracking and alerts
- **Processes** - Real-time process monitoring

## Configuration

### Backend CORS Configuration
The backend is configured to accept requests from the React frontend:

```java
// src/main/java/com/investment/config/AppConfig.java
@Bean
public WebMvcConfigurer corsConfigurer() {
    return new WebMvcConfigurer() {
        @Override
        public void addCorsMappings(CorsRegistry registry) {
            registry.addMapping("/api/**")
                    .allowedOrigins("http://localhost:3000", "http://127.0.0.1:3000")
                    .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                    .allowedHeaders("*")
                    .allowCredentials(true);
        }
    };
}
```

### Frontend API Configuration
The frontend is configured to communicate with the backend:

```typescript
// frontend/src/services/api/apiClient.ts
const apiClient = axios.create({
  baseURL: 'http://localhost:8080/investment-toolkit/api',
  withCredentials: true,
});
```

## Development Workflow

### Backend Development
1. Make changes to Java code in `src/main/java/`
2. Run tests: `./gradlew test`
3. Start application: `./gradlew bootRun`
4. API documentation: `http://localhost:8080/investment-toolkit/swagger-ui.html`

### Frontend Development
1. Navigate to `frontend/` directory
2. Make changes to React components
3. Hot reload automatically updates the browser
4. Build for production: `npm run build`

### Full Stack Development
1. Start backend: `./gradlew bootRun`
2. In another terminal, start frontend: `cd frontend && npm start`
3. Access application at `http://localhost:3000`
4. API calls will be proxied to the backend automatically

## API Documentation

### Swagger/OpenAPI
- **URL**: `http://localhost:8080/investment-toolkit/swagger-ui.html`
- **API Docs**: `http://localhost:8080/investment-toolkit/api-docs`

### Key Endpoints
- `GET /api/instruments` - List financial instruments
- `GET /api/ohlcv/{symbol}` - Get OHLCV data for symbol
- `POST /api/technical-indicators/bollinger-bands/calculate` - Calculate Bollinger Bands
- `POST /api/technical-indicators/dmi/calculate` - Calculate DMI indicators
- `GET /api/processes` - List background processes

## Database

### DuckDB Configuration
- **File**: `./data/marketdata.duckdb`
- **Connection**: Configured in `application.properties`
- **Migrations**: Handled by `DatabaseManager.java`

### Key Tables
- `instruments` - Financial instrument metadata
- `ohlcv` - Price and volume data with technical indicators
- `positions` - Portfolio positions
- `watch_list` - Tracked symbols

## Testing

### Backend Tests
```bash
./gradlew test
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Build, Export & Deployment Guide

### Prerequisites

#### System Requirements
- **Java**: OpenJDK 21 or higher
- **Node.js**: 16.x or higher
- **npm**: 8.x or higher
- **Memory**: Minimum 4GB RAM (8GB recommended for production)
- **Storage**: 2GB free space for application and database files

#### Development Tools
- **Git**: For version control
- **Gradle**: Included via wrapper (./gradlew)
- **IDE**: IntelliJ IDEA, VS Code, or similar

### Environment Configuration

#### Backend Environment Variables
Create `application-prod.properties` for production:
```properties
# Production server configuration
server.port=8080
server.servlet.context-path=/investment-toolkit

# Production database path
spring.datasource.url=**********************************************************

# Production logging
logging.level.root=WARN
logging.level.com.investment=INFO
logging.file.name=/var/log/investment-toolkit/application.log

# Production CORS (update with your domain)
cors.allowed.origins=https://yourdomain.com,https://www.yourdomain.com
```

#### Frontend Environment Variables
Create `.env.production` in frontend directory:
```bash
REACT_APP_API_BASE_URL=https://yourdomain.com/investment-toolkit/api
REACT_APP_API_TIMEOUT=30000
GENERATE_SOURCEMAP=false
```

### Build Process

#### Backend Build

##### Development Build
```bash
# Clean and compile
./gradlew clean build

# Run tests
./gradlew test

# Generate JAR file
./gradlew bootJar
```

##### Production Build
```bash
# Clean build with production profile
./gradlew clean build -Pprod

# Skip tests for faster build (not recommended)
./gradlew clean build -x test

# Build with specific JVM options
./gradlew clean build -Dorg.gradle.jvmargs="-Xmx2g -XX:MaxMetaspaceSize=512m"
```

**Build Output**: `build/libs/investment-toolkit-1.0-SNAPSHOT.jar`

#### Frontend Build

##### Development Build
```bash
cd frontend

# Install dependencies
npm install

# Development build (with source maps)
npm run build
```

##### Production Build
```bash
cd frontend

# Clean install for production
rm -rf node_modules package-lock.json
npm install --production=false

# Production build (optimized)
npm run build

# Verify build
npm run build && ls -la build/
```

**Build Output**: `frontend/build/` directory with optimized static files

### Export & Packaging

#### Backend JAR Export
```bash
# Create executable JAR
./gradlew bootJar

# Verify JAR contents
jar -tf build/libs/investment-toolkit-1.0-SNAPSHOT.jar | head -20

# Test JAR execution
java -jar build/libs/investment-toolkit-1.0-SNAPSHOT.jar --spring.profiles.active=test
```

#### Frontend Static Files Export
```bash
cd frontend

# Build and package
npm run build

# Create deployment archive
tar -czf investment-toolkit-frontend.tar.gz -C build .

# Or create ZIP archive
cd build && zip -r ../investment-toolkit-frontend.zip . && cd ..
```

#### Full Application Package
```bash
# Create complete deployment package
mkdir -p deployment-package/{backend,frontend,config,scripts}

# Copy backend JAR
cp build/libs/investment-toolkit-*.jar deployment-package/backend/

# Copy frontend build
cp -r frontend/build/* deployment-package/frontend/

# Copy configuration files
cp src/main/resources/application*.properties deployment-package/config/

# Copy startup scripts
cp start-*.sh start-*.bat deployment-package/scripts/

# Create deployment archive
tar -czf investment-toolkit-deployment.tar.gz deployment-package/
```

### Deployment Strategies

#### 1. Local/Development Deployment

##### Quick Start (Development)
```bash
# Start both services with provided scripts
./start-dev.sh    # Linux/macOS
start-dev.bat     # Windows

# Or manually:
# Terminal 1 - Backend
./gradlew bootRun

# Terminal 2 - Frontend
cd frontend && npm start
```

##### Local Production Simulation
```bash
# Build both applications
./gradlew build
cd frontend && npm run build && cd ..

# Start backend in production mode
java -jar build/libs/investment-toolkit-*.jar --spring.profiles.active=prod

# Serve frontend with a simple HTTP server
cd frontend/build
python3 -m http.server 3000  # or use nginx, apache
```

#### 2. Server Deployment

##### Linux Server Deployment
```bash
# 1. Prepare server environment
sudo apt update && sudo apt install openjdk-21-jdk nginx

# 2. Create application user
sudo useradd -r -s /bin/false investment-toolkit
sudo mkdir -p /opt/investment-toolkit/{data,logs,config}
sudo chown -R investment-toolkit:investment-toolkit /opt/investment-toolkit

# 3. Deploy backend
sudo cp build/libs/investment-toolkit-*.jar /opt/investment-toolkit/
sudo cp config/application-prod.properties /opt/investment-toolkit/config/

# 4. Create systemd service
sudo tee /etc/systemd/system/investment-toolkit.service > /dev/null <<EOF
[Unit]
Description=Investment Toolkit Backend
After=network.target

[Service]
Type=simple
User=investment-toolkit
WorkingDirectory=/opt/investment-toolkit
ExecStart=/usr/bin/java -jar investment-toolkit-*.jar --spring.config.location=config/application-prod.properties
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 5. Start and enable service
sudo systemctl daemon-reload
sudo systemctl enable investment-toolkit
sudo systemctl start investment-toolkit

# 6. Deploy frontend with Nginx
sudo cp -r frontend/build/* /var/www/html/
sudo systemctl restart nginx
```

##### Windows Server Deployment
```batch
REM 1. Install Java 21 and create directories
mkdir C:\investment-toolkit\data
mkdir C:\investment-toolkit\logs
mkdir C:\investment-toolkit\config

REM 2. Deploy backend
copy build\libs\investment-toolkit-*.jar C:\investment-toolkit\
copy config\application-prod.properties C:\investment-toolkit\config\

REM 3. Create Windows service (using NSSM or similar)
nssm install InvestmentToolkit "C:\Program Files\Java\jdk-21\bin\java.exe"
nssm set InvestmentToolkit Arguments "-jar C:\investment-toolkit\investment-toolkit-*.jar --spring.config.location=C:\investment-toolkit\config\application-prod.properties"
nssm set InvestmentToolkit Start SERVICE_AUTO_START

REM 4. Start service
nssm start InvestmentToolkit

REM 5. Deploy frontend with IIS
xcopy frontend\build\* C:\inetpub\wwwroot\ /E /Y
```

#### 3. Docker Deployment

##### Backend Dockerfile
Create `Dockerfile` in project root:
```dockerfile
FROM openjdk:21-jdk-slim

# Set working directory
WORKDIR /app

# Copy JAR file
COPY build/libs/investment-toolkit-*.jar app.jar

# Create data directory
RUN mkdir -p /app/data /app/logs

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/investment-toolkit/actuator/health || exit 1

# Run application
ENTRYPOINT ["java", "-jar", "app.jar"]
```

##### Frontend Dockerfile
Create `frontend/Dockerfile`:
```dockerfile
# Build stage
FROM node:18-alpine AS build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

##### Docker Compose
Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/investment-toolkit/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8080/investment-toolkit/api

volumes:
  data:
  logs:
```

##### Docker Deployment Commands
```bash
# Build images
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale services
docker-compose up -d --scale backend=2

# Stop services
docker-compose down
```

#### 4. Cloud Deployment

##### AWS Deployment
```bash
# Using AWS Elastic Beanstalk
eb init investment-toolkit
eb create production

# Or using ECS with Docker
aws ecs create-cluster --cluster-name investment-toolkit
aws ecs register-task-definition --cli-input-json file://task-definition.json
aws ecs create-service --cluster investment-toolkit --service-name backend --task-definition investment-toolkit:1
```

##### Google Cloud Platform
```bash
# Using Google App Engine
gcloud app deploy app.yaml

# Or using Google Kubernetes Engine
kubectl apply -f k8s-deployment.yaml
kubectl expose deployment investment-toolkit --type=LoadBalancer --port=80
```

##### Azure Deployment
```bash
# Using Azure App Service
az webapp create --resource-group myResourceGroup --plan myAppServicePlan --name investment-toolkit
az webapp deployment source config --name investment-toolkit --resource-group myResourceGroup --repo-url https://github.com/yourusername/investment-toolkit
```

### CI/CD Pipeline

#### GitHub Actions Example
Create `.github/workflows/deploy.yml`:
```yaml
name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Run backend tests
      run: ./gradlew test

    - name: Run frontend tests
      run: |
        cd frontend
        npm ci
        npm test -- --coverage --watchAll=false

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Build backend
      run: ./gradlew build

    - name: Build frontend
      run: |
        cd frontend
        npm ci
        npm run build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: application-build
        path: |
          build/libs/*.jar
          frontend/build/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Download artifacts
      uses: actions/download-artifact@v3
      with:
        name: application-build

    - name: Deploy to production
      run: |
        # Add your deployment commands here
        echo "Deploying to production server..."
```

### Monitoring and Maintenance

#### Health Checks
```bash
# Backend health check
curl -f http://localhost:8080/investment-toolkit/actuator/health

# Frontend availability check
curl -f http://localhost:3000/

# Database connectivity check
curl -f http://localhost:8080/investment-toolkit/api/instruments?page=0&size=1
```

#### Log Management
```bash
# Backend logs
tail -f logs/investment-toolkit.log

# System logs (Linux)
sudo journalctl -u investment-toolkit -f

# Docker logs
docker-compose logs -f backend
```

#### Performance Monitoring
```bash
# JVM monitoring
jstat -gc -t $(pgrep -f investment-toolkit) 5s

# Memory usage
ps aux | grep investment-toolkit

# Database size monitoring
du -sh data/marketdata.duckdb
```

#### Backup Strategy
```bash
# Database backup
cp data/marketdata.duckdb backups/marketdata-$(date +%Y%m%d).duckdb

# Configuration backup
tar -czf config-backup-$(date +%Y%m%d).tar.gz src/main/resources/application*.properties

# Automated backup script
#!/bin/bash
BACKUP_DIR="/opt/backups/investment-toolkit"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
cp /opt/investment-toolkit/data/marketdata.duckdb $BACKUP_DIR/marketdata-$DATE.duckdb
find $BACKUP_DIR -name "marketdata-*.duckdb" -mtime +7 -delete
```

### Deployment Troubleshooting

#### Common Deployment Issues

##### Backend Issues
1. **Port Already in Use**
   ```bash
   # Find process using port 8080
   lsof -i :8080
   netstat -tulpn | grep :8080

   # Kill process or change port
   kill -9 <PID>
   # Or set different port: --server.port=8081
   ```

2. **Database Connection Failed**
   ```bash
   # Check database file permissions
   ls -la data/marketdata.duckdb

   # Ensure data directory exists
   mkdir -p data

   # Check disk space
   df -h
   ```

3. **Memory Issues**
   ```bash
   # Increase JVM heap size
   java -Xmx4g -jar investment-toolkit-*.jar

   # Monitor memory usage
   jstat -gc $(pgrep -f investment-toolkit)
   ```

4. **CORS Configuration**
   ```bash
   # Update CORS origins in application.properties
   cors.allowed.origins=https://yourdomain.com

   # Or via environment variable
   export CORS_ALLOWED_ORIGINS=https://yourdomain.com
   ```

##### Frontend Issues
1. **Build Failures**
   ```bash
   # Clear npm cache
   npm cache clean --force

   # Remove node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install

   # Check Node.js version
   node --version  # Should be 16.x or higher
   ```

2. **API Connection Issues**
   ```bash
   # Verify backend is running
   curl http://localhost:8080/investment-toolkit/api/instruments

   # Check CORS configuration
   curl -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: X-Requested-With" \
        -X OPTIONS \
        http://localhost:8080/investment-toolkit/api/instruments
   ```

3. **Static File Serving**
   ```bash
   # Nginx configuration for React Router
   location / {
     try_files $uri $uri/ /index.html;
   }

   # Apache .htaccess
   RewriteEngine On
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteRule . /index.html [L]
   ```

##### Docker Issues
1. **Container Won't Start**
   ```bash
   # Check container logs
   docker logs investment-toolkit-backend

   # Inspect container
   docker inspect investment-toolkit-backend

   # Check resource usage
   docker stats
   ```

2. **Network Connectivity**
   ```bash
   # Test container networking
   docker exec -it investment-toolkit-backend curl localhost:8080/investment-toolkit/actuator/health

   # Check port mapping
   docker port investment-toolkit-backend
   ```

#### Performance Optimization

##### Backend Optimization
```bash
# JVM tuning for production
java -server \
     -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+UseStringDeduplication \
     -jar investment-toolkit-*.jar
```

##### Frontend Optimization
```bash
# Enable gzip compression in nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Set cache headers
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

##### Database Optimization
```sql
-- DuckDB optimization queries
PRAGMA memory_limit='4GB';
PRAGMA threads=4;

-- Analyze tables for better query planning
ANALYZE;

-- Create indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_ohlcv_symbol_date ON ohlcv(symbol, date);
CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol);
```

### Security Considerations

#### Production Security Checklist
- [ ] Change default passwords and secrets
- [ ] Enable HTTPS/TLS encryption
- [ ] Configure firewall rules
- [ ] Set up proper CORS origins
- [ ] Implement rate limiting
- [ ] Enable security headers
- [ ] Regular security updates
- [ ] Monitor access logs
- [ ] Backup encryption
- [ ] Network segmentation

#### Security Configuration
```bash
# Enable HTTPS with Let's Encrypt (nginx)
sudo certbot --nginx -d yourdomain.com

# Security headers in nginx
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

### Scaling Considerations

#### Horizontal Scaling
```bash
# Load balancer configuration (nginx)
upstream backend {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

# Database clustering (future enhancement)
# Consider PostgreSQL or MySQL for multi-instance deployments
```

#### Vertical Scaling
```bash
# Increase JVM heap size
export JAVA_OPTS="-Xms4g -Xmx8g"

# Increase database memory
# DuckDB: PRAGMA memory_limit='8GB';
```

## Performance Considerations

### Backend
- **Low-latency optimizations**: Primitive collections, garbage-free patterns
- **Database**: DuckDB for analytical workloads
- **Async processing**: Background operations for heavy calculations

### Frontend
- **Code splitting**: Lazy load routes
- **Caching**: API response caching
- **Optimization**: React.memo, useCallback, useMemo

## Security

### Current State
- No authentication implemented
- CORS configured for development
- Input validation on API endpoints

### Future Enhancements
- JWT authentication
- Role-based access control
- API rate limiting
- HTTPS in production

## Development Troubleshooting

### Quick Development Issues

1. **CORS Errors in Development**
   - Ensure backend CORS configuration includes `http://localhost:3000`
   - Check that backend is running on port 8080
   - Verify frontend proxy configuration in `package.json`

2. **API Connection Failed**
   - Verify backend is running: `http://localhost:8080/investment-toolkit/swagger-ui.html`
   - Check frontend API base URL in `apiClient.ts`
   - Ensure no firewall blocking ports 3000 or 8080

3. **Database Issues in Development**
   - Ensure `./data/` directory exists and is writable
   - Check DuckDB file permissions: `chmod 664 data/marketdata.duckdb`
   - Verify disk space: `df -h .`

4. **Build Failures**
   - Backend: `./gradlew clean build`
   - Frontend: `rm -rf node_modules package-lock.json && npm install`
   - Check Java version: `java -version` (should be 21+)
   - Check Node.js version: `node --version` (should be 16+)

For production deployment issues, see the [Deployment Troubleshooting](#deployment-troubleshooting) section above.

## Quick Reference

### Essential Commands

#### Development
```bash
# Start development environment
./start-dev.sh          # Linux/macOS
start-dev.bat           # Windows

# Backend only
./gradlew bootRun

# Frontend only
cd frontend && npm start

# Run tests
./gradlew test                    # Backend
cd frontend && npm test          # Frontend
```

#### Production Build
```bash
# Build everything
./gradlew build && cd frontend && npm run build

# Backend JAR
./gradlew bootJar
# Output: build/libs/investment-toolkit-*.jar

# Frontend static files
cd frontend && npm run build
# Output: frontend/build/
```

#### Deployment
```bash
# Local production test
java -jar build/libs/investment-toolkit-*.jar --spring.profiles.active=prod

# Docker deployment
docker-compose up -d

# Health checks
curl http://localhost:8080/investment-toolkit/actuator/health
curl http://localhost:3000/
```

#### Maintenance
```bash
# View logs
tail -f logs/investment-toolkit.log

# Database backup
cp data/marketdata.duckdb backups/marketdata-$(date +%Y%m%d).duckdb

# Clean build
./gradlew clean && rm -rf frontend/node_modules
```

### Port Reference
- **Backend**: 8080 (configurable via `server.port`)
- **Frontend Dev**: 3000 (React development server)
- **Frontend Prod**: 80/443 (web server dependent)
- **Database**: File-based (DuckDB, no network port)

### Key URLs
- **Frontend**: `http://localhost:3000`
- **Backend API**: `http://localhost:8080/investment-toolkit/api`
- **API Documentation**: `http://localhost:8080/investment-toolkit/swagger-ui.html`
- **Health Check**: `http://localhost:8080/investment-toolkit/actuator/health`

## Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Test both frontend and backend integration

## License

[Add your license information here]
