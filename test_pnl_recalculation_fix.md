# P&L Recalculation Bug Fix Verification

## Bug Description
The Investment Toolkit React frontend Portfolio Positions page had a data integrity bug where clicking the "Recalculate P&L" button was accidentally setting the `open_date` field in the `positions` database table to NULL for existing position records.

## Root Cause
The issue was in the `PositionsService.recalculatePnLForAllPositions()` method, which was calling `DatabaseManager.updatePosition()` with `null` values for the `openDate` and `closeDate` parameters. The SQL UPDATE statement in `updatePosition()` explicitly sets these fields, so passing `null` overwrote existing date values.

## Fix Implementation
1. **Created new database method**: Added `updatePositionPnLAndMarketData()` in `DatabaseManager` that only updates P&L and market data fields without touching date fields.

2. **Updated service layer**: Modified `PositionsService` to use the new method during P&L recalculation operations:
   - `recalculatePnLForAllPositions()` now calls `updatePositionPnLAndMarketData()`
   - `updatePositionInDatabase()` helper method now uses the date-preserving method

3. **Maintained backward compatibility**: The original `updatePosition()` method is still available for operations that need to update date fields.

## Code Changes

### DatabaseManager.java
- Added `updatePositionPnLAndMarketData()` method that excludes `open_date` and `close_date` from the UPDATE statement
- Preserves existing `updatePosition()` method for full updates

### PositionsService.java  
- Updated `recalculatePnLForAllPositions()` to use `updatePositionPnLAndMarketData()` for both OPEN and CLOSED positions
- Updated `updatePositionInDatabase()` helper method to use the date-preserving method
- Added detailed comments explaining the date preservation logic

### Test Coverage
- Added comprehensive test `should preserve open_date and close_date during P&L recalculation`
- Test verifies that the new method is called instead of the old one
- Test confirms that date fields are preserved during P&L recalculation

## Verification
The fix ensures that:
1. P&L recalculation updates only P&L-related fields (pnl_value, pnl_percent, current_price)
2. Position metadata fields like open_date, close_date, entry_price, quantity remain unchanged
3. The functionality correctly differentiates between OPEN positions (using latest OHLCV close price) and CLOSED positions (using position's close_price field)
4. All existing functionality continues to work as expected

## Test Results
✅ New test passes: `should preserve open_date and close_date during P&L recalculation`
✅ Core P&L recalculation functionality preserved
✅ Date fields are no longer overwritten during P&L operations
