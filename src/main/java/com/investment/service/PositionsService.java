package com.investment.service;

import com.investment.api.model.CreatePositionRequest;
import com.investment.api.model.UpdatePositionRequest;
import com.investment.api.model.PositionsUpdateRequest;
import com.investment.api.model.PositionsUpdateResponse;
import com.investment.api.model.PositionsTechnicalIndicatorsRequest;
import com.investment.api.model.PositionsTechnicalIndicatorsResponse;
import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.DMIRequest;
import com.investment.database.DatabaseManager;
import com.investment.model.Position;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Service for managing portfolio positions.
 * Provides CRUD operations and business logic for position management.
 */
@Service
@Log4j2
public class PositionsService   {

    private final DatabaseManager databaseManager;
    private final OHLCVService ohlcvService;
    private final RiskManagementService riskManagementService;
    private final BollingerBandService bollingerBandService;
    private final DMIService dmiService;

    @Autowired
    public PositionsService(DatabaseManager databaseManager, OHLCVService ohlcvService,
                           RiskManagementService riskManagementService,
                           BollingerBandService bollingerBandService, DMIService dmiService) {
        this.databaseManager = databaseManager;
        this.ohlcvService = ohlcvService;
        this.riskManagementService = riskManagementService;
        this.bollingerBandService = bollingerBandService;
        this.dmiService = dmiService;
    }
    
    /**
     * Create a new position.
     */
    public Position createPosition(CreatePositionRequest request) throws SQLException {
        log.info("Creating new position: {}", request);

        // Validate that the symbol exists in instruments table
        if (!databaseManager.symbolExists(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol not found in instruments: " + request.getSymbol());
        }

        Position position = request.toPosition();

        // Convert LocalDate to java.sql.Date if present
        java.sql.Date sqlOpenDate = null;
        if (position.getOpenDate() != null) {
            sqlOpenDate = java.sql.Date.valueOf(position.getOpenDate());
        }

        Long id = databaseManager.createPosition(
                position.getSymbol(),
                position.getPosition(),
                position.getSide().name(),
                position.getStatus().name(),
                position.getTradePrice(),
                position.getTradeValue(),
                position.getInitPortfolioNetValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getBbmbAdjPercent(),
                sqlOpenDate
        );

        position.setId(id);

        log.info("Created position with ID: {}", position.getId());
        return position;
    }
    
    /**
     * Get a position by ID.
     */
    public Optional<Position> getPositionById(Long id) throws SQLException {
        log.debug("Retrieving position by ID: {}", id);

        Map<String, Object> data = databaseManager.getPositionById(id);
        if (data != null) {
            return Optional.of(mapDataToPosition(data));
        }

        return Optional.empty();
    }
    
    /**
     * Get all positions with optional filtering.
     */
    public List<Position> getPositions(String symbol, Position.Status status, Position.Side side) throws SQLException {
        log.debug("Retrieving positions with filters - symbol: {}, status: {}, side: {}", symbol, status, side);

        String statusStr = status != null ? status.name() : null;
        String sideStr = side != null ? side.name() : null;

        List<Map<String, Object>> data = databaseManager.getPositions(symbol, statusStr, sideStr);
        List<Position> positions = new ArrayList<>();

        for (Map<String, Object> row : data) {
            positions.add(mapDataToPosition(row));
        }

        log.debug("Retrieved {} positions", positions.size());
        return positions;
    }
    
    /**
     * Get all open positions.
     */
    public List<Position> getOpenPositions() throws SQLException {
        return getPositions(null, Position.Status.OPEN, null);
    }
    
    /**
     * Get positions by symbol.
     */
    public List<Position> getPositionsBySymbol(String symbol) throws SQLException {
        return getPositions(symbol, null, null);
    }
    
    /**
     * Update a position.
     */
    public Position updatePosition(Long id, UpdatePositionRequest request) throws SQLException {
        log.info("Updating position ID {}: {}", id, request);

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();

        // Update fields if provided
        if (request.getLastPrice() != null) {
            position.updateMarketData(request.getLastPrice());
        }

        if (request.getRiskUnit() != null) {
            position.setRiskUnit(request.getRiskUnit());
        }

        if (request.getStopPercent() != null) {
            position.setStopPercent(request.getStopPercent());
        }

        if (request.getLastBbmb() != null) {
            position.setLastBbmb(request.getLastBbmb());
        }

        if (request.getBbmbAdjPercent() != null) {
            position.setBbmbAdjPercent(request.getBbmbAdjPercent());
        }

        if (request.getExpandOrContract() != null) {
            position.setExpandOrContract(request.getExpandOrContract());
        }

        if (request.getStatus() != null) {
            position.setStatus(request.getStatus());
        }

        // Handle close price with automatic P&L calculation
        if (request.getClosePrice() != null) {
            position.setClosePrice(request.getClosePrice());
            // Automatically calculate P&L when close price is set
            position.calculatePnLWithClosePrice(request.getClosePrice());
            log.info("Calculated P&L for position ID {}: pnlValue={}, pnlPercent={}",
                       id, position.getPnlValue(), position.getPnlPercent());
        }

        // Handle date fields with validation
        if (request.getOpenDate() != null) {
            position.setOpenDate(request.getOpenDate());
        }

        if (request.getCloseDate() != null) {
            // Validate that close date is not before open date
            if (position.getOpenDate() != null && request.getCloseDate().isBefore(position.getOpenDate())) {
                throw new IllegalArgumentException("Close date cannot be before open date");
            }
            position.setCloseDate(request.getCloseDate());
        }

        position.setUpdatedDate(LocalDateTime.now());
        
        // Convert LocalDate to java.sql.Date for database operations
        java.sql.Date sqlOpenDate = null;
        if (position.getOpenDate() != null) {
            sqlOpenDate = java.sql.Date.valueOf(position.getOpenDate());
        }

        java.sql.Date sqlCloseDate = null;
        if (position.getCloseDate() != null) {
            sqlCloseDate = java.sql.Date.valueOf(position.getCloseDate());
        }

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                position.getRiskMode() != null ? position.getRiskMode().name() : null,
                position.getConservativePeriodEndDate() != null ? java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate()) : null,
                sqlOpenDate,
                sqlCloseDate
        );

        log.info("Updated position ID: {}", id);
        return position;
    }
    
    /**
     * Update position with current market price.
     * Only updates OPEN positions - CLOSED positions are not affected by market price changes.
     */
    public Position updatePositionPrice(Long id, BigDecimal currentPrice) throws SQLException {
        log.debug("Updating position ID {} with price: {}", id, currentPrice);

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();
        BigDecimal previousLastPrice = position.getLastPrice();

        // Only update OPEN positions with market prices
        if (!position.shouldUseMarketPrice()) {
            log.debug("Skipping market price update for CLOSED position ID {}", id);
            return position; // Return unchanged position
        }

        // Try to get OHLCV data for more accurate stop-loss calculations
        try {
            OHLCVPrices ohlcvPrices = getLatestOHLCVPrices(position.getSymbol());
            if (ohlcvPrices != null) {
                // Use SQL-based OHLCV data for accurate high/low price tracking with complete historical context
                position.updateMarketDataWithOHLCVAndDatabase(ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, databaseManager);
                log.debug("Updated position ID {} with SQL-based OHLCV calculation: close={}, high={}, low={}, highestAfterTrade={}",
                           id, ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, position.getHighestAfterTrade());
            } else {
                // Fallback to simple price update
                position.updateMarketData(currentPrice);
                log.debug("Updated position ID {} with simple price update: {}", id, currentPrice);
            }
        } catch (Exception e) {
            // Fallback to simple price update if OHLCV retrieval fails
            log.warn("Failed to retrieve OHLCV data for position ID {}, using simple price update: {}", id, e.getMessage());
            position.updateMarketData(currentPrice);
        }

        // Apply enhanced risk management if risk mode is configured
        if (position.getRiskMode() != null) {
            position.updateStopValues(riskManagementService);
            log.debug("Applied enhanced risk management for position ID {}, risk mode: {}",
                        id, position.getRiskMode());
        }

        // Log the price update details
        log.info("Position ID {} ({}): updating last_price from {} to {}, P&L: {} ({}%), effective_stop: {}",
                   id, position.getSymbol(), previousLastPrice, position.getLastPrice(),
                   position.getPnlValue(), position.getPnlPercent(), position.getEffectiveStopValue());

        // Save to database with enhanced risk management fields
        java.sql.Timestamp conservativePeriodEndTimestamp = null;
        if (position.getConservativePeriodEndDate() != null) {
            conservativePeriodEndTimestamp = java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate());
        }

        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                position.getRiskMode() != null ? position.getRiskMode().name() : null,
                conservativePeriodEndTimestamp,
                null, // openDate - not updating in this method
                null  // closeDate - not updating in this method
        );

        log.debug("Successfully updated position ID {} in database with last_price: {}", id, position.getLastPrice());
        return position;
    }
    
    /**
     * Close a position.
     */
    public Position closePosition(Long id) throws SQLException {
        log.info("Closing position ID: {}", id);
        
        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }
        
        Position position = existingPosition.get();
        position.close();

        // Convert LocalDate to java.sql.Date for database operations
        java.sql.Date sqlOpenDate = null;
        if (position.getOpenDate() != null) {
            sqlOpenDate = java.sql.Date.valueOf(position.getOpenDate());
        }

        java.sql.Date sqlCloseDate = null;
        if (position.getCloseDate() != null) {
            sqlCloseDate = java.sql.Date.valueOf(position.getCloseDate());
        }

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                position.getRiskMode() != null ? position.getRiskMode().name() : null,
                position.getConservativePeriodEndDate() != null ? java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate()) : null,
                sqlOpenDate, // openDate
                sqlCloseDate  // closeDate
        );

        log.info("Closed position ID: {}", id);
        return position;
    }

    /**
     * Reopen a closed position.
     */
    public Position reopenPosition(Long id) throws SQLException {
        log.info("Reopening position ID: {}", id);

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();

        // Validate that position can be reopened
        if (position.getStatus() != Position.Status.CLOSED) {
            throw new IllegalStateException("Only CLOSED positions can be reopened. Current status: " + position.getStatus());
        }

        position.reopen();

        // Convert LocalDate to java.sql.Date for database operations
        java.sql.Date sqlOpenDate = null;
        if (position.getOpenDate() != null) {
            sqlOpenDate = java.sql.Date.valueOf(position.getOpenDate());
        }

        java.sql.Date sqlCloseDate = null;
        if (position.getCloseDate() != null) {
            sqlCloseDate = java.sql.Date.valueOf(position.getCloseDate());
        }

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                null, // riskMode
                null, // conservativePeriodEndDate
                sqlOpenDate, // openDate
                sqlCloseDate  // closeDate
        );

        log.info("Reopened position ID: {}", id);
        return position;
    }

    /**
     * Close a position with a specific close price.
     */
    public Position closePosition(Long id, BigDecimal closePrice) throws SQLException {
        log.info("Closing position ID: {} with close price: {}", id, closePrice);

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();
        position.close(closePrice);

        // Convert LocalDate to java.sql.Date for database operations
        java.sql.Date sqlOpenDate = null;
        if (position.getOpenDate() != null) {
            sqlOpenDate = java.sql.Date.valueOf(position.getOpenDate());
        }

        java.sql.Date sqlCloseDate = null;
        if (position.getCloseDate() != null) {
            sqlCloseDate = java.sql.Date.valueOf(position.getCloseDate());
        }

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                position.getRiskMode() != null ? position.getRiskMode().name() : null,
                position.getConservativePeriodEndDate() != null ? java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate()) : null,
                sqlOpenDate, // openDate
                sqlCloseDate  // closeDate
        );

        log.info("Closed position ID: {} with close price: {}", id, closePrice);
        return position;
    }

    /**
     * Update close price for a position and automatically calculate P&L.
     */
    public Position updateClosePrice(Long id, BigDecimal closePrice) throws SQLException {
        log.info("Updating close price for position ID {}: {}", id, closePrice);

        if (closePrice == null || closePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Close price must be positive");
        }

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();

        // Set close price and calculate P&L
        position.setClosePrice(closePrice);
        position.calculatePnLWithClosePrice(closePrice);
        position.setUpdatedDate(LocalDateTime.now());

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                null, // riskMode
                null, // conservativePeriodEndDate
                null, // openDate
                null  // closeDate
        );

        log.info("Updated close price for position ID {}: closePrice={}, pnlValue={}, pnlPercent={}",
                   id, closePrice, position.getPnlValue(), position.getPnlPercent());
        return position;
    }

    /**
     * Close position with a specific close price and calculate P&L.
     */
    public Position closePositionWithPrice(Long id, BigDecimal closePrice) throws SQLException {
        log.info("Closing position ID {} with close price: {}", id, closePrice);

        if (closePrice == null || closePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Close price must be positive");
        }

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();

        // Close position with price and calculate P&L
        position.close(closePrice);

        // Save to database
        databaseManager.updatePosition(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                null, // riskMode
                null, // conservativePeriodEndDate
                null, // openDate
                null  // closeDate
        );

        log.info("Closed position ID {} with close price: {} and P&L: value={}, percent={}",
                   id, closePrice, position.getPnlValue(), position.getPnlPercent());
        return position;
    }

    /**
     * Delete a position.
     */
    public boolean deletePosition(Long id) throws SQLException {
        log.info("Deleting position ID: {}", id);

        boolean deleted = databaseManager.deletePosition(id);

        if (deleted) {
            log.info("Deleted position ID: {}", id);
        } else {
            log.warn("Position not found for deletion: {}", id);
        }

        return deleted;
    }

    /**
     * Update OHLCV data for all position symbols and recalculate P&L.
     * This method performs a comprehensive update process including:
     * 1. OHLCV data updates for all position symbols
     * 2. P&L recalculation using latest market prices
     */
    public PositionsUpdateResponse updateOHLCVDataForPositions(PositionsUpdateRequest request) throws SQLException {
        log.info("Starting OHLCV data update for position symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<Position> positions = getPositions(null, null, null);

        if (positions.isEmpty()) {
            log.info("No positions found to update");
            return new PositionsUpdateResponse(0, 0, 0, 0, 0, 0,
                System.currentTimeMillis() - startTime, new ArrayList<>(), "No positions found to update");
        }

        // Extract unique symbols from positions
        List<String> symbols = positions.stream()
                .map(Position::getSymbol)
                .distinct()
                .collect(java.util.stream.Collectors.toList());

        log.info("Found {} unique symbols from {} positions to update", symbols.size(), positions.size());

        int ohlcvSuccessCount = 0;
        int ohlcvErrorCount = 0;
        int totalRecordsUpdated = 0;
        int pnlUpdatedCount = 0;
        List<PositionsUpdateResponse.SymbolUpdateResult> ohlcvResults = new ArrayList<>();

        // Phase 1: Update OHLCV data for all symbols
        for (String symbol : symbols) {
            try {
                if (!request.isDryRun()) {
                    int recordsUpdated = ohlcvService.updateOHLCVData(symbol, null, null);
                    totalRecordsUpdated += recordsUpdated;

                    if (recordsUpdated > 0) {
                        ohlcvResults.add(new PositionsUpdateResponse.SymbolUpdateResult(
                            symbol, "success", String.format("Updated %d records", recordsUpdated)));
                        ohlcvSuccessCount++;
                        log.debug("Successfully updated {} records for symbol: {}", recordsUpdated, symbol);
                    } else {
                        ohlcvResults.add(new PositionsUpdateResponse.SymbolUpdateResult(
                            symbol, "success", "No new data available"));
                        ohlcvSuccessCount++;
                        log.debug("No new data available for symbol: {}", symbol);
                    }
                } else {
                    ohlcvResults.add(new PositionsUpdateResponse.SymbolUpdateResult(
                        symbol, "success", "Dry run - would update OHLCV data"));
                    ohlcvSuccessCount++;
                }

                // Add rate limiting delay
                Thread.sleep(100);

            } catch (Exception e) {
                ohlcvResults.add(new PositionsUpdateResponse.SymbolUpdateResult(
                    symbol, "error", null, e.getMessage()));
                ohlcvErrorCount++;
                log.warn("Failed to update OHLCV data for symbol: {}", symbol, e);
            }
        }

        // Phase 2: Recalculate P&L for all positions using latest market prices
        if (!request.isDryRun()) {
            // Add a small delay to ensure OHLCV data commits are fully visible
            // This addresses potential database connection isolation issues
            try {
                Thread.sleep(500); // 500ms delay for transaction visibility
                log.debug("Added synchronization delay before P&L recalculation");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted during synchronization delay", e);
            }

            pnlUpdatedCount = recalculatePnLForAllPositions(request.getStopLossMode());
        } else {
            pnlUpdatedCount = positions.size(); // Simulate P&L updates in dry run
        }

        long processingTimeMs = System.currentTimeMillis() - startTime;
        String summary = String.format("Updated OHLCV data for %d/%d symbols (%d records) and recalculated P&L for %d positions in %d ms",
                ohlcvSuccessCount, symbols.size(), totalRecordsUpdated, pnlUpdatedCount, processingTimeMs);

        log.info("OHLCV update completed for positions: {}", summary);

        return new PositionsUpdateResponse(
                symbols.size(),
                ohlcvSuccessCount,
                ohlcvErrorCount,
                totalRecordsUpdated,
                pnlUpdatedCount,
                positions.size(),
                processingTimeMs,
                ohlcvResults,
                summary
        );
    }

    /**
     * Apply stop-loss calculation based on the specified mode.
     *
     * @param position The position to update
     * @param stopLossMode The calculation mode to use
     */
    private void applyStopLossCalculation(Position position, PositionsUpdateRequest.StopLossMode stopLossMode) {
        if (stopLossMode == PositionsUpdateRequest.StopLossMode.ENHANCED) {
            // Apply enhanced risk management using the unified stop_percentage approach
            log.debug("Applying enhanced risk management with stop_percentage={}", position.getStopPercent());
            position.updateStopValues(riskManagementService);
        } else {
            // Use standard stop-loss calculation
            log.debug("Using standard stop-loss calculation for position {}", position.getId());
            position.updateStopValues();
        }
    }

    /**
     * Recalculate P&L for all positions using status-aware logic.
     * - OPEN positions: Updated with latest market prices from OHLCV data
     * - CLOSED positions: P&L recalculated using their own close_price field
     */
    @Deprecated(since = "2024-07-10", forRemoval = true)
    public int recalculatePnLForAllPositions() throws SQLException {
        return recalculatePnLForAllPositions(PositionsUpdateRequest.StopLossMode.STANDARD);
    }

    /**
     * Recalculate P&L for all positions using status-aware logic with specified stop-loss mode.
     * - OPEN positions: Updated with latest market prices from OHLCV data
     * - CLOSED positions: P&L recalculated using their own close_price field
     */
    public int recalculatePnLForAllPositions(PositionsUpdateRequest.StopLossMode stopLossMode) throws SQLException {
        log.info("Starting status-aware P&L recalculation for all positions with stop-loss mode: {}", stopLossMode);

        List<Position> positions = getPositions(null, null, null);
        int updatedCount = 0;
        int openPositionsUpdated = 0;
        int closedPositionsRecalculated = 0;

        for (Position position : positions) {
            try {
                if (position.getStatus() == Position.Status.OPEN) {
                    // For OPEN positions: get latest OHLCV data and update with accurate high/low prices
                    OHLCVPrices ohlcvPrices = getLatestOHLCVPrices(position.getSymbol());

                    if (ohlcvPrices != null) {
                        // Update position with SQL-based OHLCV data for accurate stop-loss calculations with complete historical context
                        position.updateMarketDataWithOHLCVAndDatabase(ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, databaseManager);

                        // Apply stop-loss calculation based on specified mode
                        applyStopLossCalculation(position, stopLossMode);

                        // Save updated position to database
                        updatePositionInDatabase(position);

                        openPositionsUpdated++;
                        updatedCount++;
                        log.debug("Updated OPEN position ID {} ({}) with SQL-based OHLCV calculation: close={}, high={}, low={}, highestAfterTrade={}",
                                   position.getId(), position.getSymbol(), ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, position.getHighestAfterTrade());
                    } else {
                        log.warn("No latest OHLCV data available for OPEN position symbol: {}", position.getSymbol());
                    }
                } else {
                    // For CLOSED positions: recalculate P&L using their close_price
                    if (position.getClosePrice() != null) {
                        position.calculatePnLBasedOnStatus(null); // null market price for CLOSED positions

                        // Save the recalculated P&L to database using method that preserves date fields
                        databaseManager.updatePositionPnLAndMarketData(
                                position.getId(),
                                position.getLastPrice(),
                                position.getLastValue(),
                                position.getRiskUnit(),
                                position.getStopPercent(),
                                position.getHighestAfterTrade(),
                                position.getStopValueFromHighest(),
                                position.getLastBbmb(),
                                position.getBbmbAdjPercent(),
                                position.getStopValueFromBbmb(),
                                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                                position.getEffectiveStopValue(),
                                position.getPnlValue(),
                                position.getPnlPercent(),
                                position.getStatus().name(),
                                position.getClosePrice(),
                                null, // riskMode
                                null  // conservativePeriodEndDate
                        );

                        closedPositionsRecalculated++;
                        updatedCount++;
                        log.debug("Recalculated CLOSED position ID {} ({}): close price = {}",
                                   position.getId(), position.getSymbol(), position.getClosePrice());
                    } else {
                        log.warn("CLOSED position ID {} ({}) has no close_price - skipping P&L recalculation",
                                  position.getId(), position.getSymbol());
                    }
                }
            } catch (Exception e) {
                log.error("Failed to recalculate P&L for position ID {} ({}): {}",
                           position.getId(), position.getSymbol(), e.getMessage());
            }
        }

        log.info("Status-aware P&L recalculation completed: {} total updated ({} OPEN with market prices, {} CLOSED with close prices) out of {} positions",
                   updatedCount, openPositionsUpdated, closedPositionsRecalculated, positions.size());
        return updatedCount;
    }

    /**
     * Legacy method: Recalculate P&L for OPEN positions only using latest market prices.
     * This method maintains backward compatibility for systems that only want to update OPEN positions.
     *
     * @return Number of OPEN positions updated
     */
    public int recalculatePnLForOpenPositionsOnly() throws SQLException {
        return recalculatePnLForOpenPositionsOnly(PositionsUpdateRequest.StopLossMode.STANDARD);
    }

    /**
     * Recalculate P&L for OPEN positions only using latest market prices with specified stop-loss mode.
     *
     * @param stopLossMode The stop-loss calculation mode to use
     * @return Number of OPEN positions updated
     */
    public int recalculatePnLForOpenPositionsOnly(PositionsUpdateRequest.StopLossMode stopLossMode) throws SQLException {
        log.info("Starting P&L recalculation for OPEN positions only with stop-loss mode: {}", stopLossMode);

        List<Position> openPositions = getPositions(null, Position.Status.OPEN, null);
        int updatedCount = 0;

        for (Position position : openPositions) {
            try {
                // Get latest OHLCV data for accurate stop-loss calculations
                OHLCVPrices ohlcvPrices = getLatestOHLCVPrices(position.getSymbol());

                if (ohlcvPrices != null) {
                    // Update position with SQL-based OHLCV data for accurate high/low price tracking with complete historical context
                    position.updateMarketDataWithOHLCVAndDatabase(ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, databaseManager);

                    // Apply stop-loss calculation based on specified mode
                    applyStopLossCalculation(position, stopLossMode);

                    // Save updated position to database
                    updatePositionInDatabase(position);

                    updatedCount++;
                    log.debug("Updated P&L for OPEN position ID {} ({}) with SQL-based OHLCV calculation: close={}, high={}, low={}, highestAfterTrade={}",
                               position.getId(), position.getSymbol(), ohlcvPrices.close, ohlcvPrices.high, ohlcvPrices.low, position.getHighestAfterTrade());
                } else {
                    log.warn("No latest OHLCV data available for symbol: {}", position.getSymbol());
                }
            } catch (Exception e) {
                log.error("Failed to recalculate P&L for OPEN position ID {} ({}): {}",
                           position.getId(), position.getSymbol(), e.getMessage());
            }
        }

        log.info("P&L recalculation for OPEN positions completed: updated {} out of {} OPEN positions",
                   updatedCount, openPositions.size());
        return updatedCount;
    }

    /**
     * Get the latest market price for a symbol from OHLCV data.
     */
    private BigDecimal getLatestMarketPrice(String symbol) throws SQLException {
        OHLCVPrices prices = getLatestOHLCVPrices(symbol);
        return prices != null ? prices.close : null;
    }

    /**
     * Get the latest OHLCV prices for a symbol from OHLCV data.
     * This method returns close, high, and low prices for accurate stop-loss calculations.
     */
    private OHLCVPrices getLatestOHLCVPrices(String symbol) throws SQLException {
        try {
            log.debug("Retrieving latest OHLCV prices for symbol: {}", symbol);

            // Get the most recent OHLCV data point for the symbol
            List<Map<String, Object>> ohlcvData = databaseManager.getLatestOHLCVData(symbol, 1);

            if (!ohlcvData.isEmpty()) {
                Map<String, Object> latestData = ohlcvData.get(0);
                Object closePriceObj = latestData.get("close");
                Object highPriceObj = latestData.get("high");
                Object lowPriceObj = latestData.get("low");
                Object dateObj = latestData.get("date");
                Object bbmbObj = latestData.get("bb_middle_band");

                log.debug("Retrieved OHLCV data for {}: date={}, close={}, high={}, low={}",
                           symbol, dateObj, closePriceObj, highPriceObj, lowPriceObj);

                BigDecimal closePrice = (closePriceObj != null) ? convertToDecimal(closePriceObj) : null;
                BigDecimal highPrice = (highPriceObj != null) ? convertToDecimal(highPriceObj) : null;
                BigDecimal lowPrice = (lowPriceObj != null) ? convertToDecimal(lowPriceObj) : null;
                BigDecimal bbmb = (bbmbObj != null) ? convertToDecimal(bbmbObj) : null;

                if (closePrice != null && highPrice != null && lowPrice != null) {
                    log.debug("Successfully retrieved latest OHLCV prices for {}: close={}, high={}, low={} (date: {})",
                               symbol, closePrice, highPrice, lowPrice, dateObj);
                }

                return new OHLCVPrices(closePrice, highPrice, lowPrice, bbmb);
            } else {
                log.warn("No OHLCV data found for symbol: {}", symbol);
            }

            log.warn("Could not retrieve latest OHLCV prices for symbol: {}", symbol);
            return null;
        } catch (Exception e) {
            log.error("Error getting latest OHLCV prices for symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Helper method to convert various number types to BigDecimal.
     */
    private BigDecimal convertToDecimal(Object value) {
        if (value instanceof Double) {
            return BigDecimal.valueOf((Double) value);
        } else if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return null;
    }

    /**
     * Helper method to update a position in the database with all current values.
     * This method preserves date fields during P&L recalculation operations.
     */
    private void updatePositionInDatabase(Position position) throws SQLException {
        java.sql.Timestamp conservativePeriodEndTimestamp = null;
        if (position.getConservativePeriodEndDate() != null) {
            conservativePeriodEndTimestamp = java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate());
        }

        // Use the method that preserves date fields during P&L recalculation
        databaseManager.updatePositionPnLAndMarketData(
                position.getId(),
                position.getLastPrice(),
                position.getLastValue(),
                position.getRiskUnit(),
                position.getStopPercent(),
                position.getHighestAfterTrade(),
                position.getStopValueFromHighest(),
                position.getLastBbmb(),
                position.getBbmbAdjPercent(),
                position.getStopValueFromBbmb(),
                position.getExpandOrContract() != null ? position.getExpandOrContract().name() : null,
                position.getEffectiveStopValue(),
                position.getPnlValue(),
                position.getPnlPercent(),
                position.getStatus().name(),
                position.getClosePrice(),
                position.getRiskMode() != null ? position.getRiskMode().name() : null,
                conservativePeriodEndTimestamp
        );
    }

    /**
     * Inner class to hold OHLCV price data.
     */
    private static class OHLCVPrices {
        final BigDecimal close;
        final BigDecimal high;
        final BigDecimal low;
        final BigDecimal bbmb;

        OHLCVPrices(BigDecimal close, BigDecimal high, BigDecimal low, BigDecimal bbmb) {
            this.close = close;
            this.high = high;
            this.low = low;
            this.bbmb = bbmb;
        }
    }
    
    /**
     * Get positions that should be stopped out based on current prices.
     */
    public List<Position> getPositionsToStopOut() throws SQLException {
        log.debug("Checking for positions that should be stopped out");
        
        List<Position> openPositions = getOpenPositions();
        List<Position> toStopOut = new ArrayList<>();
        
        for (Position position : openPositions) {
            if (position.getLastPrice() != null && position.shouldStopOut(position.getLastPrice())) {
                toStopOut.add(position);
            }
        }
        
        log.debug("Found {} positions that should be stopped out", toStopOut.size());
        return toStopOut;
    }
    
    /**
     * Map database data to Position object.
     */
    private Position mapDataToPosition(Map<String, Object> data) {
        Position position = new Position();

        position.setId((Long) data.get("id"));
        position.setSymbol((String) data.get("symbol"));
        position.setPosition((BigDecimal) data.get("position"));
        position.setSide(Position.Side.valueOf((String) data.get("side")));
        position.setStatus(Position.Status.valueOf((String) data.get("status")));
        position.setTradePrice((BigDecimal) data.get("trade_price"));
        position.setTradeValue((BigDecimal) data.get("trade_value"));
        position.setInitPortfolioNetValue((BigDecimal) data.get("init_portfolio_net_value"));
        position.setLastPrice((BigDecimal) data.get("last_price"));
        position.setLastValue((BigDecimal) data.get("last_value"));
        position.setRiskUnit((BigDecimal) data.get("risk_unit"));
        position.setStopPercent((BigDecimal) data.get("stop_percent"));
        position.setHighestAfterTrade((BigDecimal) data.get("highest_after_trade"));
        position.setStopValueFromHighest((BigDecimal) data.get("stop_value_from_highest"));
        position.setLastBbmb((BigDecimal) data.get("last_bbmb"));
        position.setBbmbAdjPercent((BigDecimal) data.get("bbmb_adj_percent"));
        position.setStopValueFromBbmb((BigDecimal) data.get("stop_value_from_bbmb"));

        String expandOrContract = (String) data.get("expand_or_contract");
        if (expandOrContract != null) {
            position.setExpandOrContract(Position.ExpandOrContract.valueOf(expandOrContract));
        }

        position.setEffectiveStopValue((BigDecimal) data.get("effective_stop_value"));
        position.setPnlValue((BigDecimal) data.get("pnl_value"));
        position.setPnlPercent((BigDecimal) data.get("pnl_percent"));
        position.setClosePrice((BigDecimal) data.get("close_price"));

        // Enhanced risk management fields
        String riskMode = (String) data.get("risk_mode");
        if (riskMode != null) {
            position.setRiskMode(Position.RiskMode.valueOf(riskMode));
        }

        Timestamp conservativePeriodEndTimestamp = (Timestamp) data.get("conservative_period_end_date");
        if (conservativePeriodEndTimestamp != null) {
            position.setConservativePeriodEndDate(conservativePeriodEndTimestamp.toLocalDateTime());
        }

        Timestamp createdTimestamp = (Timestamp) data.get("created_date");
        if (createdTimestamp != null) {
            position.setCreatedDate(createdTimestamp.toLocalDateTime());
        }

        Timestamp updatedTimestamp = (Timestamp) data.get("updated_date");
        if (updatedTimestamp != null) {
            position.setUpdatedDate(updatedTimestamp.toLocalDateTime());
        }

        // Map date fields
        java.sql.Date openDate = (java.sql.Date) data.get("open_date");
        if (openDate != null) {
            position.setOpenDate(openDate.toLocalDate());
        }

        java.sql.Date closeDate = (java.sql.Date) data.get("close_date");
        if (closeDate != null) {
            position.setCloseDate(closeDate.toLocalDate());
        }

        return position;
    }

    /**
     * Initialize enhanced risk management parameters for a position.
     * This method can be used to upgrade existing positions to use the new risk management system.
     * Note: Enhanced risk management now uses the same stop_percentage for all modes.
     */
    public Position initializeEnhancedRiskManagement(Long id) throws SQLException {
        log.info("Initializing enhanced risk management for position ID {}", id);

        Optional<Position> existingPosition = getPositionById(id);
        if (existingPosition.isEmpty()) {
            throw new IllegalArgumentException("Position not found: " + id);
        }

        Position position = existingPosition.get();

        // Initialize risk parameters using the service
        riskManagementService.initializeRiskParameters(position);

        // Recalculate effective stop value with enhanced logic
        position.updateStopValues(riskManagementService);

        position.setUpdatedDate(LocalDateTime.now());

        // Save to database with enhanced risk management fields
        java.sql.Timestamp conservativePeriodEndTimestamp = null;
        if (position.getConservativePeriodEndDate() != null) {
            conservativePeriodEndTimestamp = java.sql.Timestamp.valueOf(position.getConservativePeriodEndDate());
        }

        updatePositionInDatabase(position);

        log.info("Enhanced risk management initialized for position ID {}: riskMode={}, effectiveStopValue={}",
                   id, position.getRiskMode(), position.getEffectiveStopValue());
        return position;
    }

    /**
     * Calculate technical indicators for all position symbols.
     * This method performs technical indicators calculation including:
     * 1. Bollinger Bands calculation for all position symbols
     * 2. DMI calculation for all position symbols
     */
    public PositionsTechnicalIndicatorsResponse calculateTechnicalIndicatorsForPositions(PositionsTechnicalIndicatorsRequest request) throws SQLException {
        log.info("Starting technical indicators calculation for position symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<Position> positions = getPositions(null, null, null);

        if (positions.isEmpty()) {
            log.info("No positions found to calculate technical indicators");
            return new PositionsTechnicalIndicatorsResponse(0, 0, 0, 0, 0, 0, 0, 0,
                System.currentTimeMillis() - startTime, "No positions found to calculate technical indicators");
        }

        // Get unique symbols from positions
        Set<String> symbolsSet = new HashSet<>();
        for (Position position : positions) {
            symbolsSet.add(position.getSymbol());
        }
        List<String> symbols = new ArrayList<>(symbolsSet);

        log.info("Found {} unique symbols from {} positions for technical indicators calculation",
                   symbols.size(), positions.size());

        int totalSymbols = symbols.size();
        int bollingerBandsProcessed = 0;
        int bollingerBandsSuccessCount = 0;
        int bollingerBandsErrorCount = 0;
        int dmiProcessed = 0;
        int dmiSuccessCount = 0;
        int dmiErrorCount = 0;
        int totalRecordsUpdated = 0;

        Object bollingerBandsResults = null;
        Object dmiResults = null;

        // Phase 1: Calculate Bollinger Bands if enabled
        if (request.getBollingerBands() != null && request.getBollingerBands().isEnabled()) {
            log.info("Phase 1: Calculating Bollinger Bands for {} symbols", symbols.size());

            try {
                BollingerBandRequest bbRequest = new BollingerBandRequest();
                bbRequest.setPeriod(request.getBollingerBands().getPeriod());
                bbRequest.setStdDevMultiplier(request.getBollingerBands().getStdDevMultiplier());
                bbRequest.setDryRun(request.isDryRun());
                bbRequest.setSymbols(symbols);

                // Set calculation mode
                String calculationMode = request.getBollingerBands().getCalculationMode();
                if (calculationMode != null) {
                    bbRequest.setCalculationMode(BollingerBandRequest.CalculationMode.valueOf(calculationMode));
                }

                var bbResponse = bollingerBandService.calculateBollingerBands(bbRequest);
                bollingerBandsResults = bbResponse;

                if (bbResponse != null) {
                    bollingerBandsProcessed = bbResponse.getProcessedSymbols();
                    // Assume success if processed > 0, adjust based on actual response structure
                    bollingerBandsSuccessCount = bollingerBandsProcessed;
                    bollingerBandsErrorCount = totalSymbols - bollingerBandsSuccessCount;
                    totalRecordsUpdated += bbResponse.getTotalRecordsUpdated();

                    log.info("Bollinger Bands calculation completed: processed={}, success={}, errors={}",
                               bollingerBandsProcessed, bollingerBandsSuccessCount, bollingerBandsErrorCount);
                }
            } catch (Exception e) {
                log.error("Error calculating Bollinger Bands for positions", e);
                bollingerBandsErrorCount = totalSymbols;
            }
        }

        // Phase 2: Calculate DMI if enabled
        if (request.getDmi() != null && request.getDmi().isEnabled()) {
            log.info("Phase 2: Calculating DMI for {} symbols", symbols.size());

            try {
                DMIRequest dmiRequest = new DMIRequest();
                dmiRequest.setPeriod(request.getDmi().getPeriod());
                dmiRequest.setDryRun(request.isDryRun());
                dmiRequest.setSymbols(symbols);

                // Set calculation mode
                String calculationMode = request.getDmi().getCalculationMode();
                if (calculationMode != null) {
                    dmiRequest.setCalculationMode(DMIRequest.CalculationMode.valueOf(calculationMode));
                }

                // Set calculation method
                String calculationMethod = request.getDmi().getCalculationMethod();
                if (calculationMethod != null) {
                    dmiRequest.setCalculationMethod(DMIRequest.CalculationMethod.valueOf(calculationMethod));
                }

                var dmiResponse = dmiService.calculateDMI(dmiRequest);
                dmiResults = dmiResponse;

                if (dmiResponse != null) {
                    dmiProcessed = dmiResponse.getProcessedSymbols();
                    // Assume success if processed > 0, adjust based on actual response structure
                    dmiSuccessCount = dmiProcessed;
                    dmiErrorCount = totalSymbols - dmiSuccessCount;
                    totalRecordsUpdated += dmiResponse.getTotalRecordsUpdated();

                    log.info("DMI calculation completed: processed={}, success={}, errors={}",
                               dmiProcessed, dmiSuccessCount, dmiErrorCount);
                }
            } catch (Exception e) {
                log.error("Error calculating DMI for positions", e);
                dmiErrorCount = totalSymbols;
            }
        }

        long processingTimeMs = System.currentTimeMillis() - startTime;

        // Create summary message
        StringBuilder summaryBuilder = new StringBuilder();
        summaryBuilder.append("Technical indicators calculation completed for ").append(totalSymbols).append(" symbols");

        if (request.getBollingerBands() != null && request.getBollingerBands().isEnabled()) {
            summaryBuilder.append(". Bollinger Bands: ").append(bollingerBandsSuccessCount).append(" success, ")
                         .append(bollingerBandsErrorCount).append(" errors");
        }

        if (request.getDmi() != null && request.getDmi().isEnabled()) {
            summaryBuilder.append(". DMI: ").append(dmiSuccessCount).append(" success, ")
                         .append(dmiErrorCount).append(" errors");
        }

        summaryBuilder.append(". Total records updated: ").append(totalRecordsUpdated);

        String summary = summaryBuilder.toString();

        PositionsTechnicalIndicatorsResponse response = new PositionsTechnicalIndicatorsResponse(
                totalSymbols, bollingerBandsProcessed, bollingerBandsSuccessCount, bollingerBandsErrorCount,
                dmiProcessed, dmiSuccessCount, dmiErrorCount, totalRecordsUpdated, processingTimeMs, summary);

        response.setBollingerBandsResults(bollingerBandsResults);
        response.setDmiResults(dmiResults);

        log.info("Technical indicators calculation completed for positions: {}", summary);
        return response;
    }
}
