package com.investment.service;

import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.BollingerBandResponse;
import com.investment.database.DatabaseManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for calculating and updating Bollinger Band technical indicators.
 */
@Service
@Log4j2
public class BollingerBandService {
    
    private final DatabaseManager databaseManager;
    
    public BollingerBandService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Calculate and update Bollinger Band indicators for all symbols in the database.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    public BollingerBandResponse calculateBollingerBands(BollingerBandRequest request) {
        long startTime = System.currentTimeMillis();
        
        log.info("Starting Bollinger Band calculation with parameters: {}", request);
        
        List<String> symbolsWithInsufficientData = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        int processedSymbols = 0;
        int totalRecordsUpdated = 0;
        String status = "success";
        
        try {
            // Get symbols to process based on request parameters
            List<String> symbolsToProcess = getSymbolsToProcess(request);
            log.info("Found {} symbols to process", symbolsToProcess.size());
            
            for (String symbol : symbolsToProcess) {
                try {
                    log.debug("Processing symbol: {}", symbol);
                    
                    // Check if symbol has sufficient data
                    int recordCount = databaseManager.countOhlcvRecords(symbol);
                    if (recordCount < request.getMinDataPoints()) {
                        symbolsWithInsufficientData.add(symbol);
                        log.debug("Symbol {} has insufficient data: {} records (minimum: {})", 
                                   symbol, recordCount, request.getMinDataPoints());
                        continue;
                    }
                    
                    // Handle different calculation modes
                    BollingerBandRequest.CalculationMode mode = determineCalculationMode(request);

                    if (mode == BollingerBandRequest.CalculationMode.SKIP_EXISTING && hasExistingBollingerBandData(symbol)) {
                        skippedSymbols.add(symbol);
                        log.debug("Symbol {} already has Bollinger Band data, skipping (SKIP_EXISTING mode)", symbol);
                        continue;
                    }

                    // Calculate and update Bollinger Bands for this symbol
                    int updatedRecords = calculateBollingerBandsForSymbol(symbol, request, mode);
                    
                    if (updatedRecords > 0) {
                        processedSymbols++;
                        totalRecordsUpdated += updatedRecords;
                        log.info("Successfully calculated Bollinger Bands for {}: {} records updated", 
                                  symbol, updatedRecords);
                    } else {
                        log.warn("No records updated for symbol: {}", symbol);
                    }
                    
                } catch (Exception e) {
                    failedSymbols.add(symbol);
                    String errorMsg = String.format("Failed to process symbol %s: %s", symbol, e.getMessage());
                    errors.add(errorMsg);
                    log.error("Error processing symbol {}", symbol, e);
                }
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            // Determine overall status
            if (!failedSymbols.isEmpty()) {
                status = failedSymbols.size() == symbolsToProcess.size() ? "failed" : "partial_success";
            }
            
            log.info("Bollinger Band calculation completed: {} symbols processed, {} records updated, {} failed, {} insufficient data, {} skipped in {}ms",
                       processedSymbols, totalRecordsUpdated, failedSymbols.size(), 
                       symbolsWithInsufficientData.size(), skippedSymbols.size(), processingTime);
            
            return new BollingerBandResponse(
                    status,
                    processedSymbols,
                    totalRecordsUpdated,
                    symbolsWithInsufficientData,
                    skippedSymbols,
                    failedSymbols,
                    processingTime,
                    errors,
                    request.isDryRun(),
                    new BollingerBandResponse.CalculationParameters(
                            request.getPeriod(),
                            request.getStdDevMultiplier(),
                            request.getMinDataPoints(),
                            determineCalculationMode(request)
                    )
            );
            
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("Fatal error during Bollinger Band calculation", e);
            
            errors.add("Fatal error: " + e.getMessage());
            
            return new BollingerBandResponse(
                    "failed",
                    processedSymbols,
                    totalRecordsUpdated,
                    symbolsWithInsufficientData,
                    skippedSymbols,
                    failedSymbols,
                    processingTime,
                    errors,
                    request.isDryRun(),
                    new BollingerBandResponse.CalculationParameters(
                            request.getPeriod(),
                            request.getStdDevMultiplier(),
                            request.getMinDataPoints(),
                            determineCalculationMode(request)
                    )
            );
        }
    }

    /**
     * Get the list of symbols to process based on request parameters.
     * Supports both specific symbol lists and pagination.
     */
    private List<String> getSymbolsToProcess(BollingerBandRequest request) throws SQLException {
        // If specific symbols are provided, use them (takes precedence over pagination)
        if (request.hasSpecificSymbols()) {
            log.info("Processing specific symbols: {} symbols provided", request.getSymbols().size());
            log.debug("Specific symbols: {}", request.getSymbols());

            // Validate that the symbols exist in the database
            List<String> validSymbols = new ArrayList<>();
            List<String> symbolsWithOhlcvData = databaseManager.getSymbolsWithOhlcvData();

            for (String symbol : request.getSymbols()) {
                if (symbolsWithOhlcvData.contains(symbol)) {
                    validSymbols.add(symbol);
                } else {
                    log.warn("Symbol {} not found in OHLCV data, skipping", symbol);
                }
            }

            log.info("Found {} valid symbols out of {} requested", validSymbols.size(), request.getSymbols().size());
            return validSymbols;
        }

        // Use pagination approach
        log.info("Using pagination approach for symbol selection");

        // Get total count for pagination metadata
        int totalSymbolsAvailable = databaseManager.getTotalSymbolsWithOhlcvDataCount();

        // Calculate pagination parameters
        int startIndex = request.getStartIndex();
        int effectiveEndIndex = calculateEffectiveEndIndex(request, totalSymbolsAvailable);
        int symbolsInRange = Math.max(0, Math.min(effectiveEndIndex - startIndex, totalSymbolsAvailable - startIndex));

        log.debug("Pagination: startIndex={}, effectiveEndIndex={}, symbolsInRange={}, totalAvailable={}",
                    startIndex, effectiveEndIndex, symbolsInRange, totalSymbolsAvailable);

        // Get symbols with pagination
        int limit = calculateLimit(request, totalSymbolsAvailable);
        List<String> symbols = databaseManager.getSymbolsWithOhlcvData(true, startIndex, limit);

        log.debug("Retrieved {} symbols starting from index {} with limit {}", symbols.size(), startIndex, limit);
        return symbols;
    }

    /**
     * Calculate the effective limit for database queries based on request parameters.
     */
    private int calculateLimit(BollingerBandRequest request, int totalSymbolsAvailable) {
        int startIndex = request.getStartIndex();

        // Calculate the maximum possible symbols from start index
        int maxPossibleFromStart = Math.max(0, totalSymbolsAvailable - startIndex);

        // Start with the range from pagination parameters
        int limit = maxPossibleFromStart;

        // Apply endIndex if specified
        if (request.getEndIndex() != null) {
            int requestedRange = Math.max(0, request.getEndIndex() - startIndex);
            limit = Math.min(limit, requestedRange);
        }

        // Apply maxSymbols if specified
        if (request.getMaxSymbols() > 0) {
            limit = Math.min(limit, request.getMaxSymbols());
        }

        return Math.max(0, limit);
    }

    /**
     * Calculate the effective end index based on request parameters and total available symbols.
     */
    private int calculateEffectiveEndIndex(BollingerBandRequest request, int totalSymbolsAvailable) {
        int startIndex = request.getStartIndex();

        // Start with the maximum possible end index
        int endIndex = totalSymbolsAvailable;

        // Apply explicit endIndex if specified
        if (request.getEndIndex() != null) {
            endIndex = Math.min(endIndex, request.getEndIndex());
        }

        // Apply maxSymbols limit if specified
        if (request.getMaxSymbols() > 0) {
            endIndex = Math.min(endIndex, startIndex + request.getMaxSymbols());
        }

        // Ensure end index is not less than start index
        return Math.max(startIndex, endIndex);
    }

    /**
     * Check if a symbol already has Bollinger Band data.
     */
    private boolean hasExistingBollingerBandData(String symbol) throws SQLException {
        return databaseManager.hasExistingBollingerBandData(symbol);
    }
    
    /**
     * Determine the calculation mode to use, handling backward compatibility.
     */
    private BollingerBandRequest.CalculationMode determineCalculationMode(BollingerBandRequest request) {
        // If calculationMode is explicitly set, use it
        if (request.getCalculationMode() != null) {
            return request.getCalculationMode();
        }

        // For backward compatibility with forceRecalculate boolean
        if (request.isForceRecalculate()) {
            return BollingerBandRequest.CalculationMode.FULL_RECALCULATION;
        } else {
            return BollingerBandRequest.CalculationMode.SKIP_EXISTING;
        }
    }

    /**
     * Calculate Bollinger Bands for a specific symbol using the specified calculation mode.
     */
    private int calculateBollingerBandsForSymbol(String symbol, BollingerBandRequest request, BollingerBandRequest.CalculationMode mode) throws SQLException {
        switch (mode) {
            case INCREMENTAL:
                log.debug("Using incremental calculation mode for symbol: {}", symbol);
                return databaseManager.calculateAndUpdateBollingerBandsIncremental(
                        symbol,
                        request.getPeriod(),
                        request.getStdDevMultiplier(),
                        request.isDryRun()
                );

            case FULL_RECALCULATION:
                log.debug("Using full recalculation mode for symbol: {}", symbol);
                // Clear existing data first if not in dry run mode
                if (!request.isDryRun()) {
                    databaseManager.clearBollingerBandData(symbol);
                }
                return databaseManager.calculateAndUpdateBollingerBands(
                        symbol,
                        request.getPeriod(),
                        request.getStdDevMultiplier(),
                        request.isDryRun()
                );

            case SKIP_EXISTING:
            default:
                log.debug("Using skip existing mode for symbol: {}", symbol);
                return databaseManager.calculateAndUpdateBollingerBands(
                        symbol,
                        request.getPeriod(),
                        request.getStdDevMultiplier(),
                        request.isDryRun()
                );
        }
    }
}
