package com.investment.service;

import com.investment.api.model.SyncResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.InstrumentType;
import com.investment.model.SecCompany;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for synchronizing SEC company ticker data with our instruments database.
 * Downloads SEC data and adds missing instruments to our database.
 */
@Service
@Log4j2
public class SecSynchronizationService   {

    private static final String SEC_TICKERS_URL = "https://www.sec.gov/files/company_tickers.json";
    private static final String CACHE_DIR = "./data/sec_cache";
    private static final String CACHE_FILE = "company_tickers.json";
    private static final Duration CACHE_EXPIRY = Duration.ofHours(24); // Cache for 24 hours

    private final DatabaseManager databaseManager;
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;

    // Cache for SEC data to avoid repeated parsing
    private Map<String, SecCompany> cachedSecCompanies;
    private LocalDateTime lastCacheUpdate;

    public SecSynchronizationService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
        this.objectMapper = new ObjectMapper();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();

        // Ensure cache directory exists
        ensureCacheDirectoryExists();
    }

    /**
     * Synchronize SEC data with our instruments database.
     *
     * @param dryRun If true, only report what would be added without actually adding
     * @param forceRefresh If true, force download of fresh SEC data
     * @param maxInstruments Maximum number of instruments to add in one operation
     * @return SyncResponse with results
     */
    public SyncResponse synchronizeInstruments(boolean dryRun, boolean forceRefresh, int maxInstruments) {
        log.info("Starting SEC synchronization - dryRun: {}, forceRefresh: {}, maxInstruments: {}",
                   dryRun, forceRefresh, maxInstruments);

        try {
            // Get SEC companies data
            Map<String, SecCompany> secCompanies = getSecCompanies(forceRefresh);
            log.info("Loaded {} SEC companies for synchronization", secCompanies.size());

            // Get all symbols currently in database
            Set<String> databaseSymbols = new HashSet<>(databaseManager.getAllSymbols());
            log.info("Found {} symbols in database", databaseSymbols.size());

            // Identify missing symbols (in SEC but not in database)
            List<String> missingSymbols = secCompanies.values().stream()
                    .map(SecCompany::getTicker)
                    .filter(Objects::nonNull)
                    .map(String::toUpperCase)
                    .filter(symbol -> !databaseSymbols.contains(symbol))
                    .sorted()
                    .collect(Collectors.toList());

            log.info("Found {} missing symbols: {}", missingSymbols.size(),
                       missingSymbols.size() <= 10 ? missingSymbols :
                       missingSymbols.subList(0, 10) + "... (showing first 10)");

            // Limit the number of instruments to process
            List<String> symbolsToProcess = missingSymbols.stream()
                    .limit(maxInstruments)
                    .collect(Collectors.toList());

            if (symbolsToProcess.size() < missingSymbols.size()) {
                log.info("Limited processing to {} symbols out of {} missing symbols",
                           symbolsToProcess.size(), missingSymbols.size());
            }

            int addedSymbols = 0;

            // Perform synchronization if not dry run
            if (!dryRun && !symbolsToProcess.isEmpty()) {
                log.info("Performing actual synchronization of {} symbols", symbolsToProcess.size());
                addedSymbols = addMissingInstruments(symbolsToProcess, secCompanies);
                log.info("Synchronization completed: added {} instruments", addedSymbols);
            } else if (dryRun) {
                log.info("DRY RUN: Would add {} instruments", symbolsToProcess.size());
            }

            return new SyncResponse(
                    secCompanies.size(),
                    databaseSymbols.size(),
                    missingSymbols.size(),
                    symbolsToProcess, // Return the symbols that would be/were processed
                    addedSymbols,
                    dryRun
            );

        } catch (Exception e) {
            log.error("Error during SEC synchronization", e);
            throw new RuntimeException("SEC synchronization failed: " + e.getMessage(), e);
        }
    }

    /**
     * Get cache status for monitoring and debugging.
     */
    public Map<String, Object> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("cacheDirectory", CACHE_DIR);
        status.put("cacheFile", CACHE_FILE);
        status.put("cacheExpiry", CACHE_EXPIRY.toString());
        status.put("lastCacheUpdate", lastCacheUpdate);
        status.put("cachedCompaniesCount", cachedSecCompanies != null ? cachedSecCompanies.size() : 0);

        Path cacheFilePath = Paths.get(CACHE_DIR, CACHE_FILE);
        status.put("cacheFileExists", Files.exists(cacheFilePath));

        if (Files.exists(cacheFilePath)) {
            try {
                status.put("cacheFileSize", Files.size(cacheFilePath));
                status.put("cacheFileLastModified", Files.getLastModifiedTime(cacheFilePath).toString());
            } catch (IOException e) {
                status.put("cacheFileError", e.getMessage());
            }
        }

        return status;
    }

    private void ensureCacheDirectoryExists() {
        try {
            Path cacheDir = Paths.get(CACHE_DIR);
            if (!Files.exists(cacheDir)) {
                Files.createDirectories(cacheDir);
                log.info("Created cache directory: {}", cacheDir);
            }
        } catch (IOException e) {
            log.error("Failed to create cache directory", e);
            throw new RuntimeException("Failed to create cache directory", e);
        }
    }

    /**
     * Get SEC companies data, using cache if available and not expired.
     */
    private Map<String, SecCompany> getSecCompanies(boolean forceRefresh) throws IOException, InterruptedException {
        // Return cached data if available and not expired
        if (!forceRefresh && cachedSecCompanies != null && lastCacheUpdate != null) {
            if (Duration.between(lastCacheUpdate, LocalDateTime.now()).compareTo(CACHE_EXPIRY) < 0) {
                log.info("Using cached SEC companies data");
                return cachedSecCompanies;
            }
        }

        Path cacheFilePath = Paths.get(CACHE_DIR, CACHE_FILE);

        // Check if cache file exists and is recent
        if (!forceRefresh && Files.exists(cacheFilePath)) {
            try {
                LocalDateTime fileTime = LocalDateTime.parse(
                    Files.getLastModifiedTime(cacheFilePath).toString().substring(0, 19),
                    DateTimeFormatter.ISO_LOCAL_DATE_TIME
                );

                if (Duration.between(fileTime, LocalDateTime.now()).compareTo(CACHE_EXPIRY) < 0) {
                    log.info("Loading SEC companies from cache file");
                    cachedSecCompanies = loadCompaniesFromFile(cacheFilePath);
                    lastCacheUpdate = fileTime;
                    return cachedSecCompanies;
                }
            } catch (Exception e) {
                log.warn("Error reading cache file timestamp, will download fresh data", e);
            }
        }

        // Download fresh data
        log.info("Downloading fresh SEC companies data from: {}", SEC_TICKERS_URL);
        String jsonData = downloadSecData();

        // Save to cache
        Files.writeString(cacheFilePath, jsonData);
        log.info("SEC data cached to: {}", cacheFilePath);

        // Parse and cache companies
        cachedSecCompanies = parseSecCompanies(jsonData);
        lastCacheUpdate = LocalDateTime.now();

        return cachedSecCompanies;
    }

    /**
     * Download SEC company tickers JSON data.
     */
    private String downloadSecData() throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(SEC_TICKERS_URL))
                .header("User-Agent", "InvestmentToolKit/1.0 (<EMAIL>)")
                .timeout(Duration.ofSeconds(60))
                .GET()
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            throw new IOException("Failed to download SEC data. HTTP status: " + response.statusCode());
        }

        log.info("Successfully downloaded SEC data ({} bytes)", response.body().length());
        return response.body();
    }

    /**
     * Parse SEC JSON data and extract company information.
     */
    private Map<String, SecCompany> parseSecCompanies(String jsonData) throws IOException {
        // The SEC JSON structure is: { "0": {"cik_str": "...", "ticker": "...", "title": "..."}, ... }
        Map<String, SecCompany> companies = objectMapper.readValue(
                jsonData,
                new TypeReference<Map<String, SecCompany>>() {}
        );

        log.info("Parsed {} companies from SEC data", companies.size());
        return companies;
    }

    /**
     * Load companies from cache file.
     */
    private Map<String, SecCompany> loadCompaniesFromFile(Path cacheFilePath) throws IOException {
        String jsonData = Files.readString(cacheFilePath);
        return parseSecCompanies(jsonData);
    }

    /**
     * Add missing instruments to the database.
     */
    private int addMissingInstruments(List<String> symbolsToAdd, Map<String, SecCompany> secCompanies) {
        int addedCount = 0;

        try {
            for (String symbol : symbolsToAdd) {
                // Find the company data for this symbol
                SecCompany company = findCompanyByTicker(symbol, secCompanies);

                if (company != null) {
                    // Add instrument with SEC company name
                    databaseManager.saveInstrument(
                            symbol.toUpperCase(),
                            company.getTitle(),
                            InstrumentType.US_STOCK.name()
                    );
                    addedCount++;
                    log.debug("Added instrument: {} - {}", symbol, company.getTitle());
                } else {
                    log.warn("Could not find company data for symbol: {}", symbol);
                }
            }

            log.info("Successfully added {} instruments to database", addedCount);

        } catch (Exception e) {
            log.error("Error adding instruments to database", e);
            throw new RuntimeException("Failed to add instruments: " + e.getMessage(), e);
        }

        return addedCount;
    }

    /**
     * Find company by ticker symbol.
     */
    private SecCompany findCompanyByTicker(String ticker, Map<String, SecCompany> companies) {
        return companies.values().stream()
                .filter(company -> ticker.equalsIgnoreCase(company.getTicker()))
                .findFirst()
                .orElse(null);
    }
}
