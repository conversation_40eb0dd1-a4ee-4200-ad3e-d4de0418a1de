package com.investment.service;

import com.investment.api.model.CsvUploadResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.InstrumentType;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Service for processing CSV files containing instrument data.
 */
@Service
@Log4j2
public class CsvInstrumentService   {

    // Expected CSV headers
    private static final String[] EXPECTED_HEADERS = {
        "Symbol", "Name", "Last Sale", "Net Change", "% Change", 
        "Market Cap", "Country", "IPO Year", "Volume", "Sector", "Industry"
    };
    
    private final DatabaseManager databaseManager;
    
    public CsvInstrumentService(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    /**
     * Process a CSV file containing instrument data.
     *
     * @param file The uploaded CSV file
     * @param dryRun If true, only validate and report without saving to database
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates If true, skip instruments that already exist
     * @param validateData If true, perform data validation
     * @return CsvUploadResponse with processing results
     */
    public CsvUploadResponse processCsvFile(MultipartFile file, boolean dryRun, int maxInstruments,
                                          boolean skipDuplicates, boolean validateData) {
        log.info("Starting CSV processing - file: {}, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                   file.getOriginalFilename(), dryRun, maxInstruments, skipDuplicates, validateData);
        
        List<String> validationErrors = new ArrayList<>();
        List<String> processedSymbols = new ArrayList<>();
        Set<String> existingSymbols = new HashSet<>();

        int totalRows = 0;
        int validRows = 0;
        int invalidRows = 0;
        int processedInstruments = 0;
        int skippedInstruments = 0;
        int skippedNonExistentSymbols = 0;
        int addedInstruments = 0;
        int updatedInstruments = 0;
        
        try {
            // Validate file
            if (file.isEmpty()) {
                validationErrors.add("Uploaded file is empty");
                return createErrorResponse(validationErrors, dryRun);
            }
            
            if (!isValidCsvFile(file)) {
                validationErrors.add("Invalid file format. Please upload a CSV file.");
                return createErrorResponse(validationErrors, dryRun);
            }
            
            // Get existing symbols for validation (always needed for symbol validation)
            List<String> allSymbols = databaseManager.getAllSymbols();
            existingSymbols = allSymbols != null ? new HashSet<>(allSymbols) : new HashSet<>();
            log.info("Loaded {} existing symbols from database for validation", existingSymbols.size());
            
            // Parse CSV file
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                
                CSVFormat csvFormat = CSVFormat.DEFAULT
                        .withFirstRecordAsHeader()
                        .withIgnoreHeaderCase()
                        .withTrim();
                
                CSVParser parser = csvFormat.parse(reader);
                
                // Validate headers
                if (!validateHeaders(parser.getHeaderNames(), validationErrors)) {
                    return createErrorResponse(validationErrors, dryRun);
                }
                
                // Process each record
                for (CSVRecord record : parser) {
                    totalRows++;
                    
                    // Check max instruments limit
                    if (processedInstruments >= maxInstruments) {
                        log.info("Reached maximum instruments limit: {}", maxInstruments);
                        break;
                    }
                    
                    try {
                        InstrumentData instrumentData = parseRecord(record, validateData, validationErrors);
                        
                        if (instrumentData != null) {
                            validRows++;

                            // Normalize symbol for comparison with instruments table
                            String normalizedSymbol = normalizeSymbol(instrumentData.symbol);

                            // Symbol validation: Only process symbols that exist in the instruments table
                            // Check both original and normalized symbol for maximum compatibility
                            boolean symbolExists = existingSymbols.contains(instrumentData.symbol.toUpperCase()) ||
                                                 existingSymbols.contains(normalizedSymbol);

                            if (!symbolExists) {
                                skippedNonExistentSymbols++;
                                log.info("Skipping symbol '{}' (normalized: '{}') - not found in instruments table (row {})",
                                           instrumentData.symbol, normalizedSymbol, record.getRecordNumber());
                                continue;
                            }

                            // Check for duplicates (if skipDuplicates is enabled, this would skip processing the same symbol multiple times in the CSV)
                            if (skipDuplicates && (processedSymbols.contains(instrumentData.symbol.toUpperCase()) ||
                                                  processedSymbols.contains(normalizedSymbol))) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in CSV: {}", instrumentData.symbol);
                                continue;
                            }

                            // Process the instrument (will always be an update since symbol exists)
                            if (!dryRun) {
                                instrumentData.symbol = normalizedSymbol;
                                saveInstrument(instrumentData);
                                updatedInstruments++;
                            }

                            processedInstruments++;
                            processedSymbols.add(instrumentData.symbol.toUpperCase());

                        } else {
                            invalidRows++;
                        }
                        
                    } catch (Exception e) {
                        invalidRows++;
                        validationErrors.add(String.format("Row %d: %s", record.getRecordNumber(), e.getMessage()));
                        log.warn("Error processing row {}: {}", record.getRecordNumber(), e.getMessage());
                    }
                }
                
            }
            
            log.info("CSV processing completed - Total rows: {}, Valid: {}, Invalid: {}, Processed: {}, Skipped (duplicates): {}, Skipped (non-existent): {}",
                       totalRows, validRows, invalidRows, processedInstruments, skippedInstruments, skippedNonExistentSymbols);

            return new CsvUploadResponse(
                    totalRows, validRows, invalidRows, processedInstruments,
                    skippedInstruments + skippedNonExistentSymbols, addedInstruments, updatedInstruments,
                    validationErrors, processedSymbols, dryRun
            );
            
        } catch (IOException e) {
            log.error("Error reading CSV file", e);
            validationErrors.add("Error reading CSV file: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        } catch (Exception e) {
            log.error("Unexpected error during CSV processing", e);
            validationErrors.add("Unexpected error: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        }
    }

    /**
     * Process a CSV file containing Hong Kong stock data.
     * Expected format: Symbol, Name (2 columns)
     * Headers can be in English or Traditional Chinese
     *
     * @param file The uploaded CSV file
     * @param dryRun If true, only validate and report without saving to database
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates If true, skip instruments that already exist
     * @param validateData If true, perform data validation
     * @return CsvUploadResponse with processing results
     */
    public CsvUploadResponse processHkCsvFile(MultipartFile file, boolean dryRun, int maxInstruments,
                                            boolean skipDuplicates, boolean validateData) {
        log.info("Starting Hong Kong CSV processing - file: {}, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                   file.getOriginalFilename(), dryRun, maxInstruments, skipDuplicates, validateData);

        List<String> validationErrors = new ArrayList<>();
        List<String> processedSymbols = new ArrayList<>();
        Set<String> existingSymbols = new HashSet<>();

        int totalRows = 0;
        int validRows = 0;
        int invalidRows = 0;
        int processedInstruments = 0;
        int skippedInstruments = 0;
        int addedInstruments = 0;
        int updatedInstruments = 0;

        try {
            // Validate file
            if (file.isEmpty()) {
                validationErrors.add("Uploaded file is empty");
                return createErrorResponse(validationErrors, dryRun);
            }

            if (!isValidCsvFile(file)) {
                validationErrors.add("Invalid file format. Please upload a CSV file.");
                return createErrorResponse(validationErrors, dryRun);
            }

            // Get existing symbols for duplicate checking
            List<String> allSymbols = databaseManager.getAllSymbols();
            existingSymbols = allSymbols != null ? new HashSet<>(allSymbols) : new HashSet<>();
            log.info("Loaded {} existing symbols from database for validation", existingSymbols.size());

            // Parse CSV file
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

                CSVFormat csvFormat = CSVFormat.DEFAULT
                        .withFirstRecordAsHeader()
                        .withIgnoreHeaderCase()
                        .withTrim();

                CSVParser parser = csvFormat.parse(reader);

                // Validate headers for Hong Kong CSV (2 columns expected)
                if (!validateHkHeaders(parser.getHeaderNames(), validationErrors)) {
                    return createErrorResponse(validationErrors, dryRun);
                }

                // Process each record
                for (CSVRecord record : parser) {
                    totalRows++;

                    // Check max instruments limit
                    if (processedInstruments >= maxInstruments) {
                        log.info("Reached maximum instruments limit: {}", maxInstruments);
                        break;
                    }

                    try {
                        HkInstrumentData instrumentData = parseHkRecord(record, validateData, validationErrors);

                        if (instrumentData != null) {
                            validRows++;

                            // Transform symbol to Hong Kong format
                            String transformedSymbol = transformHkSymbol(instrumentData.symbol);

                            // Check for duplicates
                            if (skipDuplicates && existingSymbols.contains(transformedSymbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol: {} (transformed: {})", instrumentData.symbol, transformedSymbol);
                                continue;
                            }

                            // Check for duplicates within the CSV file
                            if (skipDuplicates && processedSymbols.contains(transformedSymbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in CSV: {} (transformed: {})", instrumentData.symbol, transformedSymbol);
                                continue;
                            }

                            // Process the instrument
                            if (!dryRun) {
                                boolean isNewInstrument = !existingSymbols.contains(transformedSymbol);
                                saveHkInstrument(transformedSymbol, instrumentData.name);

                                if (isNewInstrument) {
                                    addedInstruments++;
                                } else {
                                    updatedInstruments++;
                                }
                            }

                            processedInstruments++;
                            processedSymbols.add(transformedSymbol);

                        } else {
                            invalidRows++;
                        }

                    } catch (Exception e) {
                        invalidRows++;
                        validationErrors.add(String.format("Row %d: %s", record.getRecordNumber(), e.getMessage()));
                        log.warn("Error processing row {}: {}", record.getRecordNumber(), e.getMessage());
                    }
                }

            }

            log.info("Hong Kong CSV processing completed - Total rows: {}, Valid: {}, Invalid: {}, Processed: {}, Skipped: {}, Added: {}, Updated: {}",
                       totalRows, validRows, invalidRows, processedInstruments, skippedInstruments, addedInstruments, updatedInstruments);

            return new CsvUploadResponse(
                    totalRows, validRows, invalidRows, processedInstruments,
                    skippedInstruments, addedInstruments, updatedInstruments,
                    validationErrors, processedSymbols, dryRun
            );

        } catch (IOException e) {
            log.error("Error reading Hong Kong CSV file", e);
            validationErrors.add("Error reading CSV file: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        } catch (Exception e) {
            log.error("Unexpected error during Hong Kong CSV processing", e);
            validationErrors.add("Unexpected error: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        }
    }

    private boolean isValidCsvFile(MultipartFile file) {
        String filename = file.getOriginalFilename();
        return filename != null && filename.toLowerCase().endsWith(".csv");
    }
    
    private boolean validateHeaders(List<String> actualHeaders, List<String> validationErrors) {
        Set<String> expectedHeaderSet = Set.of(EXPECTED_HEADERS);
        Set<String> actualHeaderSet = new HashSet<>(actualHeaders);
        
        // Check for missing required headers
        List<String> missingHeaders = new ArrayList<>();
        for (String expectedHeader : EXPECTED_HEADERS) {
            if (!actualHeaderSet.contains(expectedHeader)) {
                missingHeaders.add(expectedHeader);
            }
        }
        
        if (!missingHeaders.isEmpty()) {
            validationErrors.add("Missing required headers: " + String.join(", ", missingHeaders));
            return false;
        }
        
        return true;
    }
    
    private InstrumentData parseRecord(CSVRecord record, boolean validateData, List<String> validationErrors) {
        try {
            String symbol = record.get("Symbol");
            String name = record.get("Name");
            String marketCapStr = record.get("Market Cap");
            String country = record.get("Country");
            String ipoYearStr = record.get("IPO Year");
            String sector = record.get("Sector");
            String industry = record.get("Industry");
            
            // Validate required fields
            if (validateData) {
                if (symbol == null || symbol.trim().isEmpty()) {
                    validationErrors.add(String.format("Row %d: Symbol is required", record.getRecordNumber()));
                    return null;
                }
                
                if (name == null || name.trim().isEmpty()) {
                    validationErrors.add(String.format("Row %d: Name is required", record.getRecordNumber()));
                    return null;
                }
            }
            
            // Parse market cap
            BigDecimal marketCap = parseMarketCap(marketCapStr);
            
            // Parse IPO year
            Integer ipoYear = parseIpoYear(ipoYearStr);
            
            return new InstrumentData(
                    symbol.trim().toUpperCase(),
                    name.trim(),
                    marketCap,
                    country != null ? country.trim() : null,
                    ipoYear,
                    sector != null ? sector.trim() : null,
                    industry != null ? industry.trim() : null
            );
            
        } catch (Exception e) {
            validationErrors.add(String.format("Row %d: Error parsing data - %s", record.getRecordNumber(), e.getMessage()));
            return null;
        }
    }
    
    private BigDecimal parseMarketCap(String marketCapStr) {
        if (marketCapStr == null || marketCapStr.trim().isEmpty() || "n/a".equalsIgnoreCase(marketCapStr.trim())) {
            return null;
        }
        
        try {
            // Remove common formatting characters
            String cleanStr = marketCapStr.trim()
                    .replace("$", "")
                    .replace(",", "")
                    .replace(" ", "");
            
            // Handle suffixes like B (billion), M (million), K (thousand)
            if (cleanStr.endsWith("B") || cleanStr.endsWith("b")) {
                double value = Double.parseDouble(cleanStr.substring(0, cleanStr.length() - 1));
                return BigDecimal.valueOf(value * 1_000_000_000);
            } else if (cleanStr.endsWith("M") || cleanStr.endsWith("m")) {
                double value = Double.parseDouble(cleanStr.substring(0, cleanStr.length() - 1));
                return BigDecimal.valueOf(value * 1_000_000);
            } else if (cleanStr.endsWith("K") || cleanStr.endsWith("k")) {
                double value = Double.parseDouble(cleanStr.substring(0, cleanStr.length() - 1));
                return BigDecimal.valueOf(value * 1_000);
            } else {
                return new BigDecimal(cleanStr);
            }
        } catch (NumberFormatException e) {
            log.debug("Could not parse market cap: {}", marketCapStr);
            return null;
        }
    }
    
    private Integer parseIpoYear(String ipoYearStr) {
        if (ipoYearStr == null || ipoYearStr.trim().isEmpty() || "n/a".equalsIgnoreCase(ipoYearStr.trim())) {
            return null;
        }
        
        try {
            int year = Integer.parseInt(ipoYearStr.trim());
            // Validate reasonable year range
            if (year >= 1800 && year <= 2030) {
                return year;
            } else {
                log.debug("IPO year out of reasonable range: {}", year);
                return null;
            }
        } catch (NumberFormatException e) {
            log.debug("Could not parse IPO year: {}", ipoYearStr);
            return null;
        }
    }
    
    /**
     * Normalize symbol to handle known naming pattern differences between SEC data and CSV files.
     *
     * @param symbol The original symbol from CSV
     * @return Normalized symbol for comparison purposes
     */
    private String normalizeSymbol(String symbol) {
        if (symbol == null) {
            return null;
        }

        return symbol.trim()
                .toUpperCase()
                .replace("^", "-P")  // Convert ^ to -P (e.g., "ABR^D" → "ABR-PD")
                .replace("/", "-");  // Convert / to - (e.g., "AKO/A" → "AKO-A")
    }

    private void saveInstrument(InstrumentData data) {
        databaseManager.saveInstrumentWithDetails(
                data.symbol,
                data.name,
                InstrumentType.US_STOCK.name(), // Default to US_STOCK for CSV imports
                data.marketCap,
                data.country,
                data.ipoYear,
                data.sector,
                data.industry
        );
    }
    
    /**
     * Validate headers for Hong Kong CSV files.
     * Accepts both English and Traditional Chinese headers by position.
     */
    private boolean validateHkHeaders(List<String> actualHeaders, List<String> validationErrors) {
        if (actualHeaders.size() < 2) {
            validationErrors.add("Hong Kong CSV must have at least 2 columns: Symbol and Name");
            return false;
        }

        // We accept any headers as long as there are at least 2 columns
        // Column 1 = Symbol, Column 2 = Name (regardless of header language)
        log.info("Hong Kong CSV headers detected: {}", actualHeaders);
        return true;
    }

    /**
     * Parse a record from Hong Kong CSV file.
     */
    private HkInstrumentData parseHkRecord(CSVRecord record, boolean validateData, List<String> validationErrors) {
        try {
            // Get values by position (column 1 = symbol, column 2 = name)
            String symbol = record.size() > 0 ? record.get(0) : null;
            String name = record.size() > 1 ? record.get(1) : null;

            // Validate required fields
            if (validateData) {
                if (symbol == null || symbol.trim().isEmpty()) {
                    validationErrors.add(String.format("Row %d: Symbol (column 1) is required", record.getRecordNumber()));
                    return null;
                }

                if (name == null || name.trim().isEmpty()) {
                    validationErrors.add(String.format("Row %d: Name (column 2) is required", record.getRecordNumber()));
                    return null;
                }
            }

            return new HkInstrumentData(
                    symbol != null ? symbol.trim() : null,
                    name != null ? name.trim() : null
            );

        } catch (Exception e) {
            validationErrors.add(String.format("Row %d: Error parsing data - %s", record.getRecordNumber(), e.getMessage()));
            return null;
        }
    }

    /**
     * Transform Hong Kong symbol to standard format.
     * Examples:
     * - "00001" → "0001.HK"
     * - "1" → "0001.HK"
     * - "89618" → "89618.HK"
     */
    private String transformHkSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return null;
        }

        String cleanSymbol = symbol.trim();

        try {
            // Parse as integer to handle numeric symbols
            int symbolNum = Integer.parseInt(cleanSymbol);

            // Format with leading zeros to at least 4 digits if less than 1000
            if (symbolNum < 1000) {
                cleanSymbol = String.format("%04d", symbolNum);
            } else {
                cleanSymbol = String.valueOf(symbolNum);
            }

        } catch (NumberFormatException e) {
            // If not numeric, use as-is (shouldn't happen for HK stocks but handle gracefully)
            log.debug("Non-numeric Hong Kong symbol: {}", cleanSymbol);
        }

        // Append .HK suffix
        return cleanSymbol + ".HK";
    }

    /**
     * Save Hong Kong instrument to database.
     */
    private void saveHkInstrument(String symbol, String name) {
        databaseManager.saveInstrumentWithDetails(
                symbol,
                name,
                InstrumentType.HK_STOCK.name(),
                null, // marketCap
                "Hong Kong", // country
                null, // ipoYear
                null, // sector
                null  // industry
        );
    }

    private CsvUploadResponse createErrorResponse(List<String> validationErrors, boolean dryRun) {
        return new CsvUploadResponse(
                0, 0, 0, 0, 0, 0, 0,
                validationErrors, new ArrayList<>(), dryRun
        );
    }
    
    /**
     * Internal class to hold parsed instrument data.
     */
    private static class InstrumentData {
        String symbol;
        final String name;
        final BigDecimal marketCap;
        final String country;
        final Integer ipoYear;
        final String sector;
        final String industry;

        InstrumentData(String symbol, String name, BigDecimal marketCap, String country,
                      Integer ipoYear, String sector, String industry) {
            this.symbol = symbol;
            this.name = name;
            this.marketCap = marketCap;
            this.country = country;
            this.ipoYear = ipoYear;
            this.sector = sector;
            this.industry = industry;
        }
    }

    /**
     * Internal class to hold parsed Hong Kong instrument data.
     */
    private static class HkInstrumentData {
        final String symbol;
        final String name;

        HkInstrumentData(String symbol, String name) {
            this.symbol = symbol;
            this.name = name;
        }
    }
}
