package com.investment.service;

import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import com.investment.model.Position;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * Service for sophisticated risk management calculations.
 * Implements enhanced stop-loss logic based on position age and market conditions.
 */
@Service
@Log4j2
public class RiskManagementService   {

    // Risk management constants
    private static final int INITIAL_PERIOD_DAYS = 2;
    private static final int CONSERVATIVE_PERIOD_DAYS = 2;
    private static final int MIN_HISTORICAL_DAYS = 3; // Need T-2 and T-1 data
    
    @Autowired
    private DatabaseManager databaseManager;
    
    /**
     * Calculate the effective stop value using enhanced risk management logic.
     *
     * @param position The position to calculate stop value for
     * @return The calculated effective stop value, or null if insufficient data
     */
    public BigDecimal calculateEnhancedEffectiveStopValue(Position position) {
        if (position == null || position.getSymbol() == null || position.getOpenDate() == null) {
            log.warn("Invalid position data for enhanced risk calculation, position={}", position == null ? "null" : position);
            return null;
        }

        try {
            // Calculate position age in trading days
            int positionAgeInDays = calculateTradingDaysAge(position);
            log.debug("Position {} age: {} trading days", position.getId(), positionAgeInDays);

            // Determine risk mode based on position age and market conditions
            Position.RiskMode newRiskMode = determineRiskMode(position, positionAgeInDays);
            log.info("Determined risk mode for position {}: {}", position.getId(), newRiskMode);

            // Update position risk mode if changed
            if (position.getRiskMode() != newRiskMode) {
                position.setRiskMode(newRiskMode);
                log.info("Position {} risk mode changed to: {}", position.getId(), newRiskMode);
            }

            // Calculate effective stop value based on risk mode
            return calculateStopValueForRiskMode(position, newRiskMode);

        } catch (Exception e) {
            log.error("Error calculating enhanced effective stop value for position {}: {}",
                        position.getId(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Calculate position age in trading days based on actual OHLCV data availability.
     * For OPEN positions: count trading days from openDate to most recent OHLCV record
     * For CLOSED positions: count trading days from openDate to closeDate (inclusive)
     *
     * @param position The position to calculate trading days for
     * @return Number of trading days based on OHLCV data availability
     */
    private int calculateTradingDaysAge(Position position) {
        if (position == null || position.getSymbol() == null || position.getOpenDate() == null) {
            log.warn("Invalid position data for trading days calculation");
            return 0;
        }

        String symbol = position.getSymbol();
        LocalDate openDate = position.getOpenDate();
        LocalDate endDate = position.getCloseDate(); // null for OPEN positions

        log.debug("Calculating trading days age for position {} with symbol: {}, openDate: {}, closeDate: {}",
                    position.getId(), symbol, openDate, endDate);

        try {
            // Use DatabaseManager method to count OHLCV records within the date range
            int tradingDays = databaseManager.countOhlcvRecordsInDateRange(symbol, openDate, endDate);

            log.debug("Position {} has {} trading days from {} to {}",
                       position.getId(), tradingDays, openDate,
                       endDate != null ? endDate : "latest available");
            return tradingDays;

        } catch (Exception e) {
            log.error("Error calculating trading days age for position {}: {}",
                        position.getId(), e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * Determine the appropriate risk mode based on position age and market conditions.
     */
    private Position.RiskMode determineRiskMode(Position position, int positionAgeInDays) {
        // Initial Period (Days 1-2): Always use aggressive approach
        if (positionAgeInDays <= INITIAL_PERIOD_DAYS) {
            log.debug("Position {} in initial period, using AGGRESSIVE mode", position.getId());
            return Position.RiskMode.AGGRESSIVE;
        }
        
        // Check if currently in a forced conservative period
        if (isInConservativePeriod(position)) {
            log.debug("Position {} in forced conservative period until {}", 
                        position.getId(), position.getConservativePeriodEndDate());
            return Position.RiskMode.CONSERVATIVE;
        }
        
        // Ongoing Period (Day 3 onwards): Analyze Bollinger Band trends
        return analyzeBollingerBandTrend(position);
    }
    
    /**
     * Check if position is currently in a forced conservative period.
     */
    private boolean isInConservativePeriod(Position position) {
        LocalDateTime conservativeEndDate = position.getConservativePeriodEndDate();
        return conservativeEndDate != null && LocalDateTime.now().isBefore(conservativeEndDate);
    }
    
    /**
     * Analyze Bollinger Band trend to determine risk mode.
     * Case 2.1 - Expanding Risk: Continue aggressive if distance is expanding
     * Case 2.2 - Contracting Risk: Switch to conservative if distance is contracting
     */
    private Position.RiskMode analyzeBollingerBandTrend(Position position) {
        try {
            // Get recent OHLCV data with Bollinger Bands
            List<OHLCV> recentData = getRecentOHLCVData(position.getSymbol(), MIN_HISTORICAL_DAYS);
            
            if (recentData.size() < MIN_HISTORICAL_DAYS) {
                log.warn("Insufficient historical data for position {}, using AGGRESSIVE mode", 
                           position.getId());
                return Position.RiskMode.AGGRESSIVE;
            }
            
            // Sort by date descending (most recent first) - create mutable list first
            List<OHLCV> sortedData = new ArrayList<>(recentData);
            sortedData.sort((a, b) -> b.getDate().compareTo(a.getDate()));
            
            OHLCV t1Data = sortedData.get(0); // T-1 (previous trading day)
            OHLCV t2Data = sortedData.get(1); // T-2 (day before previous)
            
            // Calculate distances from candlestick body bottom to BB middle
            double t1Distance = calculateDistanceFromBBMiddle(t1Data, position.getSide());
            double t2Distance = calculateDistanceFromBBMiddle(t2Data, position.getSide());
            
            log.debug("Position {} BB distance analysis: T-1={}, T-2={}", 
                        position.getId(), t1Distance, t2Distance);
            
            if (t1Distance >= t2Distance) {
                // Expanding away from middle band - continue aggressive
                log.debug("Position {} trend expanding, using AGGRESSIVE mode", position.getId());
                return Position.RiskMode.AGGRESSIVE;
            } else {
                // Contracting toward middle band - switch to conservative for 2 days
                log.info("Position {} trend contracting, switching to CONSERVATIVE mode for {} days", 
                           position.getId(), CONSERVATIVE_PERIOD_DAYS);
                
                // Set conservative period end date
                LocalDateTime conservativeEndDate = LocalDateTime.now().plusDays(CONSERVATIVE_PERIOD_DAYS);
                position.setConservativePeriodEndDate(conservativeEndDate);
                
                return Position.RiskMode.CONSERVATIVE;
            }
            
        } catch (Exception e) {
            log.error("Error analyzing Bollinger Band trend for position {}: {}", 
                        position.getId(), e.getMessage(), e);
            // Default to aggressive on error
            return Position.RiskMode.AGGRESSIVE;
        }
    }
    
    /**
     * Get recent OHLCV data with Bollinger Bands for analysis.
     */
    private List<OHLCV> getRecentOHLCVData(String symbol, int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days + 5); // Extra buffer for weekends
        
        try {
            List<OHLCV> data = databaseManager.getOHLCVData(symbol, startDate, endDate);
            
            // Filter out records without Bollinger Band data
            return data.stream()
                      .filter(ohlcv -> ohlcv.getBbMiddleBand() != null)
                      .toList();
                      
        } catch (Exception e) {
            log.error("Error retrieving OHLCV data for symbol {}: {}", symbol, e.getMessage(), e);
            return List.of();
        }
    }
    
    /**
     * Calculate distance from candlestick body bottom to Bollinger Band middle.
     * For BUY positions: distance = min(open, close) - bb_middle
     * For SELL positions: distance = bb_middle - max(open, close)
     */
    private double calculateDistanceFromBBMiddle(OHLCV ohlcv, Position.Side side) {
        if (ohlcv.getBbMiddleBand() == null) {
            return 0.0;
        }
        
        double bbMiddle = ohlcv.getBbMiddleBand();
        
        if (side == Position.Side.BUY) {
            // For BUY positions, use candlestick body bottom (min of open/close)
            double bodyBottom = Math.min(ohlcv.getOpen(), ohlcv.getClose());
            return Math.abs(bodyBottom - bbMiddle);
        } else {
            // For SELL positions, use candlestick body top (max of open/close)
            double bodyTop = Math.max(ohlcv.getOpen(), ohlcv.getClose());
            return Math.abs(bbMiddle - bodyTop);
        }
    }
    
    /**
     * Calculate stop value based on the determined risk mode.
     * All risk modes use the same stop_percentage from the positions table.
     */
    private BigDecimal calculateStopValueForRiskMode(Position position, Position.RiskMode riskMode) {
        BigDecimal stopPercent = position.getStopPercent();
        log.debug("calculateStopValueForRiskMode for position {} with risk mode: {}", position.getId(), riskMode);

        if (stopPercent == null || position.getHighestAfterTrade() == null) {
            log.warn("Insufficient stop parameters for position {}", position.getId());
            return null;
        }

        // Calculate stop value based on position side
        if (position.getSide() == Position.Side.BUY) {
            // TODO can we just use stopValueFromHighest and stopValueFromBbmb of Position here?
            BigDecimal stopValueFromHighest = position.getStopValueFromHighest();
            BigDecimal stopValueFromBbmb = position.getStopValueFromBbmb();
            if (stopValueFromHighest == null) {
                stopValueFromHighest = position.getHighestAfterTrade()
                        .multiply(BigDecimal.ONE.subtract(stopPercent))
                        .setScale(6, RoundingMode.HALF_UP);
            }

            if (stopValueFromBbmb == null) {
                BigDecimal lastBbmb = position.getLastBbmb();
                if (lastBbmb != null) {
                    stopValueFromBbmb = lastBbmb
                            .multiply(BigDecimal.ONE.subtract(stopPercent))
                            .setScale(6, RoundingMode.HALF_UP);
                } else {
                    log.warn("Cannot calculate stopValueFromBbmb for position {}: lastBbmb is null", position.getId());
                    // Use stopValueFromHighest as fallback
                    stopValueFromBbmb = stopValueFromHighest;
                }
            }

            // Handle cases where one or both values might still be null
            if (stopValueFromHighest == null && stopValueFromBbmb == null) {
                log.warn("Both stopValueFromHighest and stopValueFromBbmb are null for position {}", position.getId());
                return null;
            } else if (stopValueFromHighest == null) {
                return stopValueFromBbmb;
            } else if (stopValueFromBbmb == null) {
                return stopValueFromHighest;
            }

            log.info("stopValueFromHighest={}, stopValueFromBbmb={}", stopValueFromHighest, stopValueFromBbmb);

            return switch (riskMode) {
                case AGGRESSIVE -> stopValueFromHighest.min(stopValueFromBbmb);
                case CONSERVATIVE -> stopValueFromHighest.max(stopValueFromBbmb);
                default ->
                    // Conservative mode (i.e. Standard mode) should be applied if riskMode is null
                        stopValueFromHighest.max(stopValueFromBbmb);
            };
        } else {
            // TODO can we just use stopValueFromHighest and stopValueFromBbmb of Position here?
            BigDecimal stopValueFromHighest = position.getStopValueFromHighest();
            BigDecimal stopValueFromBbmb = position.getStopValueFromBbmb();
            if (stopValueFromHighest == null) {
                stopValueFromHighest = position.getHighestAfterTrade()
                        .multiply(BigDecimal.ONE.add(stopPercent))
                        .setScale(6, RoundingMode.HALF_UP);
            }

            if (stopValueFromBbmb == null) {
                BigDecimal lastBbmb = position.getLastBbmb();
                if (lastBbmb != null) {
                    stopValueFromBbmb = lastBbmb
                            .multiply(BigDecimal.ONE.add(stopPercent))
                            .setScale(6, RoundingMode.HALF_UP);
                } else {
                    log.warn("Cannot calculate stopValueFromBbmb for position {}: lastBbmb is null", position.getId());
                    // Use stopValueFromHighest as fallback
                    stopValueFromBbmb = stopValueFromHighest;
                }
            }

            // Handle cases where one or both values might still be null
            if (stopValueFromHighest == null && stopValueFromBbmb == null) {
                log.warn("Both stopValueFromHighest and stopValueFromBbmb are null for position {}", position.getId());
                return null;
            } else if (stopValueFromHighest == null) {
                return stopValueFromBbmb;
            } else if (stopValueFromBbmb == null) {
                return stopValueFromHighest;
            }

            return switch (riskMode) {
                case AGGRESSIVE -> stopValueFromHighest.max(stopValueFromBbmb);
                case CONSERVATIVE -> stopValueFromHighest.min(stopValueFromBbmb);
                default ->
                    // Conservative mode (i.e. Standard mode) should be applied if riskMode is null
                        stopValueFromHighest.min(stopValueFromBbmb);
            };
        }
    }
    
    /**
     * Initialize enhanced risk management parameters for a new position.
     * Note: Enhanced risk management now uses the same stop_percentage for all modes.
     */
    public void initializeRiskParameters(Position position) {
        position.setRiskMode(Position.RiskMode.AGGRESSIVE); // Start with aggressive

        log.info("Initialized risk parameters for position {}: using stop_percentage={}%",
                   position.getId(),
                   position.getStopPercent() != null ? position.getStopPercent().multiply(BigDecimal.valueOf(100)) : "null");
    }
}
