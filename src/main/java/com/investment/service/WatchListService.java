package com.investment.service;

import com.investment.api.model.CreateWatchListRequest;
import com.investment.api.model.RecalculatePerformanceResponse;
import com.investment.api.model.UpdateWatchListRequest;
import com.investment.api.model.WatchListUpdateRequest;
import com.investment.api.model.WatchListUpdateResponse;
import com.investment.api.model.BollingerBandRequest;
import com.investment.api.model.DMIRequest;
import com.investment.api.model.CsvUploadResponse;
import com.investment.database.DatabaseManager;
import com.investment.model.OHLCV;
import com.investment.model.WatchListItem;

import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing watch list operations.
 * Follows garbage-free patterns for low-latency trading systems.
 */
@Service
@Log4j2
public class WatchListService   {

    private final DatabaseManager databaseManager;
    private final OHLCVService ohlcvService;
    private final BollingerBandService bollingerBandService;
    private final DMIService dmiService;

    public WatchListService(DatabaseManager databaseManager, OHLCVService ohlcvService,
                           BollingerBandService bollingerBandService, DMIService dmiService) {
        this.databaseManager = databaseManager;
        this.ohlcvService = ohlcvService;
        this.bollingerBandService = bollingerBandService;
        this.dmiService = dmiService;
    }

    /**
     * Create a new watch list item.
     */
    public WatchListItem createWatchListItem(CreateWatchListRequest request) throws SQLException {
        log.info("Creating new watch list item: {}", request);

        // Validate that the symbol exists in instruments table
        if (!databaseManager.symbolExists(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol not found in instruments: " + request.getSymbol());
        }

        // Check if symbol already exists in watch list
        if (databaseManager.symbolExistsInWatchList(request.getSymbol())) {
            throw new IllegalArgumentException("Symbol already exists in watch list: " + request.getSymbol());
        }

        WatchListItem item = request.toWatchListItem();

        Long id = databaseManager.createWatchListItem(
                item.getDisplayIndex(),
                item.getSymbol(),
                java.sql.Date.valueOf(item.getStartDate()),
                item.getRemarks()
        );

        item.setId(id);

        log.info("Created watch list item with ID: {}", item.getId());
        return item;
    }

    /**
     * Get a watch list item by ID.
     */
    public Optional<WatchListItem> getWatchListItemById(Long id) throws SQLException {
        log.debug("Retrieving watch list item by ID: {}", id);

        Map<String, Object> data = databaseManager.getWatchListItemById(id);
        if (data != null) {
            return Optional.of(mapDataToWatchListItem(data));
        }

        return Optional.empty();
    }

    /**
     * Get all watch list items ordered by display index.
     */
    public List<WatchListItem> getAllWatchListItems() throws SQLException {
        log.debug("Retrieving all watch list items");

        List<Map<String, Object>> data = databaseManager.getWatchListItems();
        List<WatchListItem> items = new ArrayList<>();

        for (Map<String, Object> row : data) {
            items.add(mapDataToWatchListItem(row));
        }

        log.debug("Retrieved {} watch list items", items.size());
        return items;
    }

    /**
     * Update a watch list item.
     */
    public WatchListItem updateWatchListItem(Long id, UpdateWatchListRequest request) throws SQLException {
        log.info("Updating watch list item ID: {} with request: {}", id, request);

        if (!request.hasUpdates()) {
            throw new IllegalArgumentException("No update fields provided");
        }

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();

        // Update fields if provided
        if (request.getDisplayIndex() != null) {
            item.setDisplayIndex(request.getDisplayIndex());
        }

        if (request.getRemarks() != null) {
            item.setRemarks(request.getRemarks());
        }

        if (request.hasPerformanceUpdates()) {
            item.updatePerformance(
                    request.getOneMonthPerf(),
                    request.getThreeMonthPerf(),
                    request.getSixMonthPerf()
            );
        }

        // Save to database
        databaseManager.updateWatchListItem(
                item.getId(),
                item.getDisplayIndex(),
                item.getRemarks(),
                item.getOneMonthPerf(),
                item.getThreeMonthPerf(),
                item.getSixMonthPerf()
        );

        log.info("Updated watch list item ID: {}", id);
        return item;
    }

    /**
     * Update performance metrics for a watch list item.
     */
    public WatchListItem updateWatchListPerformance(Long id, BigDecimal oneMonthPerf, 
                                                   BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) throws SQLException {
        log.info("Updating performance for watch list item ID: {}", id);

        Optional<WatchListItem> existingItem = getWatchListItemById(id);
        if (existingItem.isEmpty()) {
            throw new IllegalArgumentException("Watch list item not found: " + id);
        }

        WatchListItem item = existingItem.get();
        item.updatePerformance(oneMonthPerf, threeMonthPerf, sixMonthPerf);

        // Save to database
        databaseManager.updateWatchListPerformance(id, oneMonthPerf, threeMonthPerf, sixMonthPerf);

        log.info("Updated performance for watch list item ID: {}", id);
        return item;
    }

    /**
     * Delete a watch list item.
     */
    public boolean deleteWatchListItem(Long id) throws SQLException {
        log.info("Deleting watch list item ID: {}", id);

        boolean deleted = databaseManager.deleteWatchListItem(id);

        if (deleted) {
            log.info("Deleted watch list item ID: {}", id);
        } else {
            log.warn("Watch list item not found for deletion: {}", id);
        }

        return deleted;
    }

    /**
     * Bulk reorder watch list items.
     */
    public void reorderWatchListItems(Map<Long, Integer> idToIndexMap) throws SQLException {
        log.info("Reordering {} watch list items", idToIndexMap.size());

        // Validate all IDs exist
        for (Long id : idToIndexMap.keySet()) {
            if (getWatchListItemById(id).isEmpty()) {
                throw new IllegalArgumentException("Watch list item not found: " + id);
            }
        }

        // Perform bulk update
        databaseManager.updateWatchListDisplayIndexes(idToIndexMap);

        log.info("Successfully reordered {} watch list items", idToIndexMap.size());
    }

    /**
     * Calculate and update performance metrics for all watch list items.
     * Returns detailed results of the operation.
     */
    public RecalculatePerformanceResponse calculateAndUpdateAllPerformance() throws SQLException {
        log.info("Calculating performance for all watch list items");

        long startTime = System.currentTimeMillis();
        List<WatchListItem> items = getAllWatchListItems();

        List<String> successfulSymbols = new ArrayList<>();
        List<String> failedSymbols = new ArrayList<>();
        List<String> skippedSymbols = new ArrayList<>();

        int successfulUpdates = 0;
        int failedUpdates = 0;
        int skippedItems = 0;

        for (WatchListItem item : items) {
            try {
                // Calculate performance based on OHLCV data
                PerformanceMetrics metrics = calculatePerformanceMetrics(item.getSymbol(), item.getStartDate());

                if (metrics.hasData()) {
                    databaseManager.updateWatchListPerformance(
                            item.getId(),
                            metrics.getOneMonthPerf(),
                            metrics.getThreeMonthPerf(),
                            metrics.getSixMonthPerf()
                    );
                    successfulUpdates++;
                    successfulSymbols.add(item.getSymbol());
                    log.debug("Successfully updated performance for symbol: {}", item.getSymbol());
                } else {
                    skippedItems++;
                    skippedSymbols.add(item.getSymbol());
                    log.debug("Skipped symbol due to insufficient data: {}", item.getSymbol());
                }
            } catch (Exception e) {
                failedUpdates++;
                failedSymbols.add(item.getSymbol());
                log.warn("Failed to calculate performance for symbol: {}", item.getSymbol(), e);
            }
        }

        long processingTime = System.currentTimeMillis() - startTime;

        RecalculatePerformanceResponse response = new RecalculatePerformanceResponse(
                items.size(),
                successfulUpdates,
                failedUpdates,
                skippedItems,
                successfulSymbols,
                failedSymbols,
                skippedSymbols,
                processingTime
        );

        log.info("Performance recalculation completed: {}", response.getSummaryMessage());
        return response;
    }

    /**
     * Calculate performance metrics for a symbol from start date.
     * Performance is calculated as percentage change from N months ago to current price.
     */
    private PerformanceMetrics calculatePerformanceMetrics(String symbol, LocalDate startDate) {
        log.debug("Calculating performance metrics for {} from {}", symbol, startDate);

        try {
            // Get current date and calculate reference dates
            LocalDate currentDate = LocalDate.now();
            LocalDate oneMonthAgo = currentDate.minusMonths(1);
            LocalDate threeMonthsAgo = currentDate.minusMonths(3);
            LocalDate sixMonthsAgo = currentDate.minusMonths(6);

            // Get current price (most recent OHLCV data)
            List<OHLCV> currentData = databaseManager.getOHLCVData(symbol, currentDate.minusDays(7), currentDate);
            if (currentData.isEmpty()) {
                log.warn("No recent OHLCV data found for symbol: {}", symbol);
                return new PerformanceMetrics();
            }

            // Get the most recent closing price
            double currentPrice = currentData.get(currentData.size() - 1).getClose();
            log.debug("Current price for {}: {}", symbol, currentPrice);

            PerformanceMetrics metrics = new PerformanceMetrics();

            // Calculate 1-month performance
            BigDecimal oneMonthPerf = calculatePerformanceForPeriod(symbol, oneMonthAgo, currentPrice);
            if (oneMonthPerf != null) {
                metrics.setOneMonthPerf(oneMonthPerf);
                log.debug("1-month performance for {}: {}%", symbol, oneMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            // Calculate 3-month performance
            BigDecimal threeMonthPerf = calculatePerformanceForPeriod(symbol, threeMonthsAgo, currentPrice);
            if (threeMonthPerf != null) {
                metrics.setThreeMonthPerf(threeMonthPerf);
                log.debug("3-month performance for {}: {}%", symbol, threeMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            // Calculate 6-month performance
            BigDecimal sixMonthPerf = calculatePerformanceForPeriod(symbol, sixMonthsAgo, currentPrice);
            if (sixMonthPerf != null) {
                metrics.setSixMonthPerf(sixMonthPerf);
                log.debug("6-month performance for {}: {}%", symbol, sixMonthPerf.multiply(BigDecimal.valueOf(100)));
            }

            return metrics;

        } catch (Exception e) {
            log.error("Error calculating performance metrics for symbol: {}", symbol, e);
            return new PerformanceMetrics();
        }
    }

    /**
     * Calculate performance percentage for a specific period.
     * Returns null if insufficient data is available.
     */
    private BigDecimal calculatePerformanceForPeriod(String symbol, LocalDate referenceDate, double currentPrice) {
        try {
            // Get historical data around the reference date (±3 days to handle weekends/holidays)
            LocalDate startDate = referenceDate.minusDays(3);
            LocalDate endDate = referenceDate.plusDays(3);

            List<OHLCV> historicalData = databaseManager.getOHLCVData(symbol, startDate, endDate);
            if (historicalData.isEmpty()) {
                log.debug("No historical data found for {} around {}", symbol, referenceDate);
                return null;
            }

            // Find the closest date to our reference date
            OHLCV closestData = historicalData.stream()
                    .min((a, b) -> {
                        long diffA = Math.abs(a.getDate().toEpochDay() - referenceDate.toEpochDay());
                        long diffB = Math.abs(b.getDate().toEpochDay() - referenceDate.toEpochDay());
                        return Long.compare(diffA, diffB);
                    })
                    .orElse(null);

            if (closestData == null) {
                return null;
            }

            double historicalPrice = closestData.getClose();

            // Calculate percentage change: (current - historical) / historical
            double performanceDecimal = (currentPrice - historicalPrice) / historicalPrice;

            // Convert to BigDecimal with appropriate precision
            return BigDecimal.valueOf(performanceDecimal).setScale(6, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("Error calculating performance for {} at {}: {}", symbol, referenceDate, e.getMessage());
            return null;
        }
    }

    /**
     * Map database data to WatchListItem object.
     */
    private WatchListItem mapDataToWatchListItem(Map<String, Object> data) {
        WatchListItem item = new WatchListItem();

        item.setId((Long) data.get("id"));
        item.setDisplayIndex((Integer) data.get("display_index"));
        item.setSymbol((String) data.get("symbol"));
        
        java.sql.Date startDate = (java.sql.Date) data.get("start_date");
        if (startDate != null) {
            item.setStartDate(startDate.toLocalDate());
        }
        
        item.setRemarks((String) data.get("remarks"));
        item.setOneMonthPerf((BigDecimal) data.get("one_mo_perf"));
        item.setThreeMonthPerf((BigDecimal) data.get("three_mo_perf"));
        item.setSixMonthPerf((BigDecimal) data.get("six_mo_perf"));
        item.setBullishBbStreak((Integer) data.get("bullish_bb_streak"));
        item.setDmiBullishStreak((Integer) data.get("dmi_bullish_streak"));
        item.setCombinedSignalStreak((Integer) data.get("combined_signal_streak"));

        Timestamp createdTimestamp = (Timestamp) data.get("created_date");
        if (createdTimestamp != null) {
            item.setCreatedDate(createdTimestamp.toLocalDateTime());
        }

        Timestamp updatedTimestamp = (Timestamp) data.get("updated_date");
        if (updatedTimestamp != null) {
            item.setUpdatedDate(updatedTimestamp.toLocalDateTime());
        }

        return item;
    }

    /**
     * Update OHLCV data for all watch list symbols.
     * This method focuses only on OHLCV data updates for all watch list symbols.
     * Technical indicator recalculation has been moved to separate methods.
     */
    public WatchListUpdateResponse updateOHLCVDataForWatchList(WatchListUpdateRequest request) throws SQLException {
        log.info("Starting OHLCV data update for watch list symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<WatchListItem> watchListItems = getAllWatchListItems();

        WatchListUpdateResponse response = new WatchListUpdateResponse(watchListItems.size(), 0, 0);
        List<WatchListUpdateResponse.SymbolUpdateResult> ohlcvResults = new ArrayList<>();

        // Update OHLCV data for each symbol
        log.info("Updating OHLCV data for {} symbols", watchListItems.size());
        int ohlcvSuccessCount = 0;
        int ohlcvErrorCount = 0;
        int totalRecordsUpdated = 0;

        for (WatchListItem item : watchListItems) {
            String symbol = item.getSymbol();
            try {
                if (!request.isDryRun()) {
                    int recordsUpdated = ohlcvService.updateOHLCVData(symbol, null, null);
                    totalRecordsUpdated += recordsUpdated;

                    if (recordsUpdated > 0) {
                        ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                            symbol, "success", String.format("Updated %d records", recordsUpdated)));
                        ohlcvSuccessCount++;
                        log.debug("Successfully updated {} records for symbol: {}", recordsUpdated, symbol);
                    } else {
                        ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                            symbol, "success", "No new data available"));
                        ohlcvSuccessCount++;
                        log.debug("No new data available for symbol: {}", symbol);
                    }
                } else {
                    ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                        symbol, "success", "Dry run - would update OHLCV data"));
                    ohlcvSuccessCount++;
                }

                // Add rate limiting delay
                Thread.sleep(100);

            } catch (Exception e) {
                ohlcvResults.add(new WatchListUpdateResponse.SymbolUpdateResult(
                    symbol, "error", null, e.getMessage()));
                ohlcvErrorCount++;
                log.warn("Failed to update OHLCV data for symbol: {}", symbol, e);
            }
        }

        // Update response with OHLCV results
        response.setOhlcvSuccessCount(ohlcvSuccessCount);
        response.setOhlcvErrorCount(ohlcvErrorCount);
        response.setTotalRecordsUpdated(totalRecordsUpdated);
        response.getPhases().setOhlcv(ohlcvResults);

        // Finalize response
        long processingTime = System.currentTimeMillis() - startTime;
        response.setProcessingTimeMs(processingTime);

        String summary = String.format(
            "Watch list OHLCV update completed: %d/%d symbols updated successfully, %d total records updated (%.1fs)",
            ohlcvSuccessCount, watchListItems.size(), totalRecordsUpdated, processingTime / 1000.0);
        response.setSummary(summary);

        log.info("Watch list OHLCV update completed: {}", summary);
        return response;
    }

    /**
     * Recalculate Bollinger Bands for all watch list symbols using INCREMENTAL mode.
     * This method extracts the Bollinger Bands recalculation logic that was previously
     * part of the updateOHLCVDataForWatchList method.
     */
    public WatchListUpdateResponse recalculateBollingerBandsForWatchList(WatchListUpdateRequest request) throws SQLException {
        log.info("Starting Bollinger Bands recalculation for watch list symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<WatchListItem> watchListItems = getAllWatchListItems();
        List<String> symbols = watchListItems.stream()
                .map(WatchListItem::getSymbol)
                .collect(Collectors.toList());

        WatchListUpdateResponse response = new WatchListUpdateResponse(watchListItems.size(), 0, 0);

        try {
            // Recalculate Bollinger Bands using INCREMENTAL mode
            BollingerBandRequest bbRequest = new BollingerBandRequest();
            bbRequest.setCalculationMode(BollingerBandRequest.CalculationMode.INCREMENTAL);
            bbRequest.setSymbols(symbols);
            bbRequest.setDryRun(request.isDryRun());

            var bbResponse = bollingerBandService.calculateBollingerBands(bbRequest);
            response.setBollingerBandsProcessed(bbResponse.getProcessedSymbols());

            Map<String, Object> bbResults = new HashMap<>();
            bbResults.put("processedSymbols", bbResponse.getProcessedSymbols());
            bbResults.put("totalRecordsUpdated", bbResponse.getTotalRecordsUpdated());
            bbResults.put("status", bbResponse.getStatus());
            response.getPhases().setBollingerBands(bbResults);

            log.info("Bollinger Bands recalculation completed: {} symbols processed",
                       bbResponse.getProcessedSymbols());

        } catch (Exception e) {
            log.error("Failed to recalculate Bollinger Bands for watch list", e);
            throw new SQLException("Bollinger Bands recalculation failed: " + e.getMessage(), e);
        }

        // Finalize response
        long processingTime = System.currentTimeMillis() - startTime;
        response.setProcessingTimeMs(processingTime);

        String summary = String.format(
            "Watch list Bollinger Bands recalculation completed: %d symbols processed (%.1fs)",
            response.getBollingerBandsProcessed(), processingTime / 1000.0);
        response.setSummary(summary);

        log.info("Watch list Bollinger Bands recalculation completed: {}", summary);
        return response;
    }

    /**
     * Recalculate DMI indicators for all watch list symbols using INCREMENTAL mode.
     * This method extracts the DMI recalculation logic that was previously
     * part of the updateOHLCVDataForWatchList method.
     */
    public WatchListUpdateResponse recalculateDMIForWatchList(WatchListUpdateRequest request) throws SQLException {
        log.info("Starting DMI recalculation for watch list symbols: {}", request);

        long startTime = System.currentTimeMillis();
        List<WatchListItem> watchListItems = getAllWatchListItems();
        List<String> symbols = watchListItems.stream()
                .map(WatchListItem::getSymbol)
                .collect(Collectors.toList());

        WatchListUpdateResponse response = new WatchListUpdateResponse(watchListItems.size(), 0, 0);

        try {
            // Recalculate DMI using INCREMENTAL mode
            DMIRequest dmiRequest = new DMIRequest();
            dmiRequest.setCalculationMode(DMIRequest.CalculationMode.INCREMENTAL);
            dmiRequest.setSymbols(symbols);
            dmiRequest.setDryRun(request.isDryRun());

            var dmiResponse = dmiService.calculateDMI(dmiRequest);
            response.setDmiProcessed(dmiResponse.getProcessedSymbols());

            Map<String, Object> dmiResults = new HashMap<>();
            dmiResults.put("processedSymbols", dmiResponse.getProcessedSymbols());
            dmiResults.put("totalRecordsUpdated", dmiResponse.getTotalRecordsUpdated());
            dmiResults.put("status", dmiResponse.getStatus());
            response.getPhases().setDmi(dmiResults);

            log.info("DMI recalculation completed: {} symbols processed",
                       dmiResponse.getProcessedSymbols());

        } catch (Exception e) {
            log.error("Failed to recalculate DMI indicators for watch list", e);
            throw new SQLException("DMI recalculation failed: " + e.getMessage(), e);
        }

        // Finalize response
        long processingTime = System.currentTimeMillis() - startTime;
        response.setProcessingTimeMs(processingTime);

        String summary = String.format(
            "Watch list DMI recalculation completed: %d symbols processed (%.1fs)",
            response.getDmiProcessed(), processingTime / 1000.0);
        response.setSummary(summary);

        log.info("Watch list DMI recalculation completed: {}", summary);
        return response;
    }

    /**
     * Process a CSV file containing TradingView StockScreener data for watch list.
     * Expected CSV format with headers: Symbol, Description, Price, Price - Currency, Price Change % 1 day,
     * Volume 1 day, Relative Volume 1 day, Market capitalization, Market capitalization - Currency,
     * Sector, Analyst Rating, Performance % 1 month, Performance % 3 months, Performance % 6 months
     *
     * @param file The uploaded CSV file
     * @param dryRun If true, only validate and report without saving to database
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates If true, skip symbols that already exist in watch list
     * @param validateData If true, perform data validation
     * @return CsvUploadResponse with processing results
     */
    public CsvUploadResponse processTradingViewCsvFile(MultipartFile file, boolean dryRun, int maxInstruments,
                                                      boolean skipDuplicates, boolean validateData) {
        log.info("Starting TradingView CSV processing - file: {}, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                   file.getOriginalFilename(), dryRun, maxInstruments, skipDuplicates, validateData);

        int totalRows = 0;
        int validRows = 0;
        int invalidRows = 0;
        int processedInstruments = 0;
        int skippedInstruments = 0;
        int skippedNonExistentSymbols = 0;
        int addedInstruments = 0;
        List<String> validationErrors = new ArrayList<>();
        List<String> processedSymbols = new ArrayList<>();
        Set<String> existingWatchListSymbols = new HashSet<>();
        Set<String> existingInstrumentSymbols = new HashSet<>();

        try {
            // Get existing symbols for validation
            List<String> allInstrumentSymbols = databaseManager.getAllSymbols();
            existingInstrumentSymbols = allInstrumentSymbols != null ? new HashSet<>(allInstrumentSymbols) : new HashSet<>();
            log.info("Loaded {} existing instrument symbols from database for validation", existingInstrumentSymbols.size());

            // Get existing watch list symbols
            List<WatchListItem> watchListItems = getAllWatchListItems();
            existingWatchListSymbols = watchListItems.stream()
                    .map(WatchListItem::getSymbol)
                    .collect(Collectors.toSet());
            log.info("Loaded {} existing watch list symbols for duplicate checking", existingWatchListSymbols.size());

            // Parse CSV file
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

                CSVFormat csvFormat = CSVFormat.DEFAULT
                        .withFirstRecordAsHeader()
                        .withIgnoreHeaderCase()
                        .withTrim();

                CSVParser parser = csvFormat.parse(reader);

                // Validate headers
                if (!validateTradingViewHeaders(parser.getHeaderNames(), validationErrors)) {
                    return createErrorResponse(validationErrors, dryRun);
                }

                // Process each record
                for (CSVRecord record : parser) {
                    totalRows++;

                    // Check max instruments limit
                    if (processedInstruments >= maxInstruments) {
                        log.info("Reached maximum instruments limit: {}", maxInstruments);
                        break;
                    }

                    try {
                        TradingViewData tradingViewData = parseTradingViewRecord(record, validateData, validationErrors);

                        if (tradingViewData != null) {
                            validRows++;

                            String symbol = tradingViewData.symbol.toUpperCase();

                            // Check if symbol exists in instruments table
                            if (!existingInstrumentSymbols.contains(symbol)) {
                                skippedNonExistentSymbols++;
                                log.debug("Skipping symbol not found in instruments table: {}", symbol);
                                continue;
                            }

                            // Check for duplicates in watch list
                            if (skipDuplicates && existingWatchListSymbols.contains(symbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in watch list: {}", symbol);
                                continue;
                            }

                            // Check for duplicates within the CSV file
                            if (skipDuplicates && processedSymbols.contains(symbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in CSV: {}", symbol);
                                continue;
                            }

                            processedInstruments++;
                            processedSymbols.add(symbol);

                            // Save to database if not dry run
                            if (!dryRun) {
                                saveTradingViewWatchListItem(tradingViewData);
                                addedInstruments++;
                                log.debug("Added watch list item for symbol: {}", symbol);
                            }
                        } else {
                            invalidRows++;
                        }

                    } catch (Exception e) {
                        invalidRows++;
                        validationErrors.add(String.format("Row %d: %s", record.getRecordNumber(), e.getMessage()));
                        log.warn("Error processing row {}: {}", record.getRecordNumber(), e.getMessage());
                    }
                }
            }

            log.info("TradingView CSV processing completed - Total rows: {}, Valid: {}, Invalid: {}, Processed: {}, Skipped (duplicates): {}, Skipped (non-existent): {}",
                       totalRows, validRows, invalidRows, processedInstruments, skippedInstruments, skippedNonExistentSymbols);

            return new CsvUploadResponse(
                    totalRows, validRows, invalidRows, processedInstruments,
                    skippedInstruments + skippedNonExistentSymbols, addedInstruments, 0, // updatedInstruments = 0 for watch list
                    validationErrors, processedSymbols, dryRun
            );

        } catch (Exception e) {
            log.error("Error processing TradingView CSV file", e);
            validationErrors.add("CSV processing failed: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        }
    }

    /**
     * Process a CSV file containing TradingView StockScreener data for Hong Kong stocks in watch list.
     * Expected CSV format with headers: Symbol, Description, Price, Price - Currency, Price Change % 1 day,
     * Volume 1 day, Relative Volume 1 day, Market capitalization, Market capitalization - Currency,
     * Sector, Analyst Rating, Performance % 1 month, Performance % 3 months, Performance % 6 months
     *
     * @param file The uploaded CSV file
     * @param dryRun If true, only validate and report without saving to database
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates If true, skip symbols that already exist in watch list
     * @param validateData If true, perform data validation
     * @return CsvUploadResponse with processing results
     */
    public CsvUploadResponse processHkTradingViewCsvFile(MultipartFile file, boolean dryRun, int maxInstruments,
                                                        boolean skipDuplicates, boolean validateData) {
        log.info("Starting Hong Kong TradingView CSV processing - file: {}, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                   file.getOriginalFilename(), dryRun, maxInstruments, skipDuplicates, validateData);

        int totalRows = 0;
        int validRows = 0;
        int invalidRows = 0;
        int processedInstruments = 0;
        int skippedInstruments = 0;
        int skippedNonExistentSymbols = 0;
        int addedInstruments = 0;
        List<String> validationErrors = new ArrayList<>();
        List<String> processedSymbols = new ArrayList<>();
        Set<String> existingWatchListSymbols = new HashSet<>();
        Set<String> existingInstrumentSymbols = new HashSet<>();

        try {
            // Get existing symbols for validation
            List<String> allInstrumentSymbols = databaseManager.getAllSymbols();
            existingInstrumentSymbols = allInstrumentSymbols != null ? new HashSet<>(allInstrumentSymbols) : new HashSet<>();
            log.info("Loaded {} existing instrument symbols from database for validation", existingInstrumentSymbols.size());

            // Get existing watch list symbols
            List<WatchListItem> watchListItems = getAllWatchListItems();
            existingWatchListSymbols = watchListItems.stream()
                    .map(WatchListItem::getSymbol)
                    .collect(Collectors.toSet());
            log.info("Loaded {} existing watch list symbols for duplicate checking", existingWatchListSymbols.size());

            // Parse CSV file
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

                CSVFormat csvFormat = CSVFormat.DEFAULT
                        .withFirstRecordAsHeader()
                        .withIgnoreHeaderCase()
                        .withTrim();

                CSVParser parser = csvFormat.parse(reader);

                // Validate headers
                if (!validateTradingViewHeaders(parser.getHeaderNames(), validationErrors)) {
                    return createErrorResponse(validationErrors, dryRun);
                }

                // Process each record
                for (CSVRecord record : parser) {
                    totalRows++;

                    // Check max instruments limit
                    if (processedInstruments >= maxInstruments) {
                        log.info("Reached maximum instruments limit: {}", maxInstruments);
                        break;
                    }

                    try {
                        HkTradingViewData hkTradingViewData = parseHkTradingViewRecord(record, validateData, validationErrors);

                        if (hkTradingViewData != null) {
                            validRows++;

                            String transformedSymbol = hkTradingViewData.symbol;

                            // Check if transformed symbol exists in instruments table
                            if (!existingInstrumentSymbols.contains(transformedSymbol)) {
                                skippedNonExistentSymbols++;
                                log.debug("Skipping symbol not found in instruments table: {}", transformedSymbol);
                                continue;
                            }

                            // Check for duplicates in watch list
                            if (skipDuplicates && existingWatchListSymbols.contains(transformedSymbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in watch list: {}", transformedSymbol);
                                continue;
                            }

                            // Check for duplicates within the CSV file
                            if (skipDuplicates && processedSymbols.contains(transformedSymbol)) {
                                skippedInstruments++;
                                log.debug("Skipping duplicate symbol in CSV: {}", transformedSymbol);
                                continue;
                            }

                            processedInstruments++;
                            processedSymbols.add(transformedSymbol);

                            // Save to database if not dry run
                            if (!dryRun) {
                                saveHkTradingViewWatchListItem(hkTradingViewData);
                                addedInstruments++;
                                log.debug("Added watch list item for symbol: {}", transformedSymbol);
                            }
                        } else {
                            invalidRows++;
                        }

                    } catch (Exception e) {
                        invalidRows++;
                        validationErrors.add(String.format("Row %d: %s", record.getRecordNumber(), e.getMessage()));
                        log.warn("Error processing row {}: {}", record.getRecordNumber(), e.getMessage());
                    }
                }
            }

            log.info("Hong Kong TradingView CSV processing completed - Total rows: {}, Valid: {}, Invalid: {}, Processed: {}, Skipped (duplicates): {}, Skipped (non-existent): {}",
                       totalRows, validRows, invalidRows, processedInstruments, skippedInstruments, skippedNonExistentSymbols);

            return new CsvUploadResponse(
                    totalRows, validRows, invalidRows, processedInstruments,
                    skippedInstruments + skippedNonExistentSymbols, addedInstruments, 0, // updatedInstruments = 0 for watch list
                    validationErrors, processedSymbols, dryRun
            );

        } catch (Exception e) {
            log.error("Error processing Hong Kong TradingView CSV file", e);
            validationErrors.add("Hong Kong TradingView CSV processing failed: " + e.getMessage());
            return createErrorResponse(validationErrors, dryRun);
        }
    }

    /**
     * Validate TradingView CSV headers.
     */
    private boolean validateTradingViewHeaders(List<String> headers, List<String> validationErrors) {
        Set<String> requiredHeaders = Set.of(
                "Symbol", "Performance % 1 month", "Performance % 3 months", "Performance % 6 months"
        );

        Set<String> headerSet = headers.stream()
                .map(String::trim)
                .collect(Collectors.toSet());

        for (String required : requiredHeaders) {
            if (!headerSet.contains(required)) {
                validationErrors.add("Missing required header: " + required);
            }
        }

        return validationErrors.isEmpty();
    }

    /**
     * Parse a TradingView CSV record.
     */
    private TradingViewData parseTradingViewRecord(CSVRecord record, boolean validateData, List<String> validationErrors) {
        try {
            String symbol = record.get("Symbol");
            if (symbol == null || symbol.trim().isEmpty()) {
                throw new IllegalArgumentException("Symbol is required");
            }

            // Parse performance percentages and convert from percentage format (divide by 100)
            BigDecimal oneMonthPerf = parsePerformancePercentage(record.get("Performance % 1 month"));
            BigDecimal threeMonthPerf = parsePerformancePercentage(record.get("Performance % 3 months"));
            BigDecimal sixMonthPerf = parsePerformancePercentage(record.get("Performance % 6 months"));

            return new TradingViewData(symbol.trim().toUpperCase(), oneMonthPerf, threeMonthPerf, sixMonthPerf);

        } catch (Exception e) {
            if (validateData) {
                throw e;
            }
            return null;
        }
    }

    /**
     * Parse performance percentage string and convert to decimal (divide by 100).
     */
    private BigDecimal parsePerformancePercentage(String percentageStr) {
        if (percentageStr == null || percentageStr.trim().isEmpty() || percentageStr.equals("—") || percentageStr.equals("-")) {
            return null;
        }

        try {
            // Remove % sign if present and parse
            String cleanStr = percentageStr.trim().replace("%", "");
            BigDecimal percentage = new BigDecimal(cleanStr);
            // Convert percentage to decimal (divide by 100)
            return percentage.divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid performance percentage format: " + percentageStr);
        }
    }

    /**
     * Parse a Hong Kong TradingView CSV record.
     */
    private HkTradingViewData parseHkTradingViewRecord(CSVRecord record, boolean validateData, List<String> validationErrors) {
        try {
            String symbol = record.get("Symbol");
            if (symbol == null || symbol.trim().isEmpty()) {
                throw new IllegalArgumentException("Symbol is required");
            }

            // Transform Hong Kong symbol to standard format (e.g., "2282" → "2282.HK", "136" → "0136.HK")
            String transformedSymbol = transformHkSymbol(symbol.trim());

            // Parse performance percentages and convert from percentage format (divide by 100)
            BigDecimal oneMonthPerf = parsePerformancePercentage(record.get("Performance % 1 month"));
            BigDecimal threeMonthPerf = parsePerformancePercentage(record.get("Performance % 3 months"));
            BigDecimal sixMonthPerf = parsePerformancePercentage(record.get("Performance % 6 months"));

            return new HkTradingViewData(transformedSymbol, oneMonthPerf, threeMonthPerf, sixMonthPerf);

        } catch (Exception e) {
            if (validateData) {
                throw e;
            }
            return null;
        }
    }

    /**
     * Transform Hong Kong symbol to standard format.
     * Examples:
     * - "2282" → "2282.HK"
     * - "136" → "0136.HK" (pad to 4 digits with leading zeros)
     * - "99889" → "99889.HK" (5+ digits remain unchanged)
     */
    private String transformHkSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return null;
        }

        String cleanSymbol = symbol.trim();

        try {
            // Parse as integer to handle numeric symbols
            int symbolNum = Integer.parseInt(cleanSymbol);

            // Format with leading zeros to at least 4 digits if less than 10000
            if (symbolNum < 10000) {
                cleanSymbol = String.format("%04d", symbolNum);
            } else {
                cleanSymbol = String.valueOf(symbolNum);
            }

        } catch (NumberFormatException e) {
            // If not numeric, use as-is (shouldn't happen for HK stocks but handle gracefully)
            log.debug("Non-numeric Hong Kong symbol: {}", cleanSymbol);
        }

        // Append .HK suffix
        return cleanSymbol + ".HK";
    }

    /**
     * Save TradingView data as watch list item.
     */
    private void saveTradingViewWatchListItem(TradingViewData data) throws SQLException {
        // Get next available display index
        int displayIndex = databaseManager.getNextWatchListDisplayIndex();

        // Set start_date to current date (following existing business logic)
        LocalDate startDate = LocalDate.now();

        // Create watch list item with auto-generated fields
        Long id = databaseManager.createWatchListItem(
                displayIndex,
                data.symbol,
                java.sql.Date.valueOf(startDate),
                "updated from TradingView StockScreener CSV"
        );

        // Update performance metrics if available
        if (data.oneMonthPerf != null || data.threeMonthPerf != null || data.sixMonthPerf != null) {
            databaseManager.updateWatchListPerformance(id, data.oneMonthPerf, data.threeMonthPerf, data.sixMonthPerf);
        }
    }

    /**
     * Save Hong Kong TradingView data as watch list item.
     */
    private void saveHkTradingViewWatchListItem(HkTradingViewData data) throws SQLException {
        // Get next available display index
        int displayIndex = databaseManager.getNextWatchListDisplayIndex();

        // Set start_date to current date (following existing business logic)
        LocalDate startDate = LocalDate.now();

        // Create watch list item with auto-generated fields
        Long id = databaseManager.createWatchListItem(
                displayIndex,
                data.symbol,
                java.sql.Date.valueOf(startDate),
                "updated from TradingView StockScreener CSV"
        );

        // Update performance metrics if available
        if (data.oneMonthPerf != null || data.threeMonthPerf != null || data.sixMonthPerf != null) {
            databaseManager.updateWatchListPerformance(id, data.oneMonthPerf, data.threeMonthPerf, data.sixMonthPerf);
        }
    }

    /**
     * Create error response for CSV processing failures.
     */
    private CsvUploadResponse createErrorResponse(List<String> validationErrors, boolean dryRun) {
        return new CsvUploadResponse(
                0, 0, 0, 0, 0, 0, 0,
                validationErrors, new ArrayList<>(), dryRun
        );
    }

    /**
     * Data class for TradingView CSV data.
     */
    private static class TradingViewData {
        final String symbol;
        final BigDecimal oneMonthPerf;
        final BigDecimal threeMonthPerf;
        final BigDecimal sixMonthPerf;

        TradingViewData(String symbol, BigDecimal oneMonthPerf, BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) {
            this.symbol = symbol;
            this.oneMonthPerf = oneMonthPerf;
            this.threeMonthPerf = threeMonthPerf;
            this.sixMonthPerf = sixMonthPerf;
        }
    }

    /**
     * Data class for Hong Kong TradingView CSV data.
     */
    private static class HkTradingViewData {
        final String symbol;
        final BigDecimal oneMonthPerf;
        final BigDecimal threeMonthPerf;
        final BigDecimal sixMonthPerf;

        HkTradingViewData(String symbol, BigDecimal oneMonthPerf, BigDecimal threeMonthPerf, BigDecimal sixMonthPerf) {
            this.symbol = symbol;
            this.oneMonthPerf = oneMonthPerf;
            this.threeMonthPerf = threeMonthPerf;
            this.sixMonthPerf = sixMonthPerf;
        }
    }

    /**
     * Helper class for performance metrics calculation.
     */
    private static class PerformanceMetrics {
        private BigDecimal oneMonthPerf;
        private BigDecimal threeMonthPerf;
        private BigDecimal sixMonthPerf;

        public boolean hasData() {
            return oneMonthPerf != null || threeMonthPerf != null || sixMonthPerf != null;
        }

        public BigDecimal getOneMonthPerf() { return oneMonthPerf; }
        public BigDecimal getThreeMonthPerf() { return threeMonthPerf; }
        public BigDecimal getSixMonthPerf() { return sixMonthPerf; }

        public void setOneMonthPerf(BigDecimal oneMonthPerf) { this.oneMonthPerf = oneMonthPerf; }
        public void setThreeMonthPerf(BigDecimal threeMonthPerf) { this.threeMonthPerf = threeMonthPerf; }
        public void setSixMonthPerf(BigDecimal sixMonthPerf) { this.sixMonthPerf = sixMonthPerf; }
    }
}
