package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Response model for positions technical indicators calculation operations.
 */
@Schema(description = "Response for positions technical indicators calculation operations")
public class PositionsTechnicalIndicatorsResponse {

    @Schema(description = "Total number of unique symbols processed", example = "15")
    private int totalSymbols;

    @Schema(description = "Number of symbols processed for Bollinger Bands", example = "15")
    private int bollingerBandsProcessed;

    @Schema(description = "Number of symbols with successful Bollinger Bands calculations", example = "14")
    private int bollingerBandsSuccessCount;

    @Schema(description = "Number of symbols with failed Bollinger Bands calculations", example = "1")
    private int bollingerBandsErrorCount;

    @Schema(description = "Number of symbols processed for DMI", example = "15")
    private int dmiProcessed;

    @Schema(description = "Number of symbols with successful DMI calculations", example = "14")
    private int dmiSuccessCount;

    @Schema(description = "Number of symbols with failed DMI calculations", example = "1")
    private int dmiErrorCount;

    @Schema(description = "Total number of technical indicator records updated", example = "280")
    private int totalRecordsUpdated;

    @Schema(description = "Processing time in milliseconds", example = "45000")
    private long processingTimeMs;

    @Schema(description = "Summary of the calculation operation")
    private String summary;

    @Schema(description = "Detailed results for Bollinger Bands calculation")
    private Object bollingerBandsResults;

    @Schema(description = "Detailed results for DMI calculation")
    private Object dmiResults;

    public PositionsTechnicalIndicatorsResponse() {
    }

    public PositionsTechnicalIndicatorsResponse(int totalSymbols, 
                                              int bollingerBandsProcessed, int bollingerBandsSuccessCount, int bollingerBandsErrorCount,
                                              int dmiProcessed, int dmiSuccessCount, int dmiErrorCount,
                                              int totalRecordsUpdated, long processingTimeMs, String summary) {
        this.totalSymbols = totalSymbols;
        this.bollingerBandsProcessed = bollingerBandsProcessed;
        this.bollingerBandsSuccessCount = bollingerBandsSuccessCount;
        this.bollingerBandsErrorCount = bollingerBandsErrorCount;
        this.dmiProcessed = dmiProcessed;
        this.dmiSuccessCount = dmiSuccessCount;
        this.dmiErrorCount = dmiErrorCount;
        this.totalRecordsUpdated = totalRecordsUpdated;
        this.processingTimeMs = processingTimeMs;
        this.summary = summary;
    }

    // Getters and setters
    public int getTotalSymbols() {
        return totalSymbols;
    }

    public void setTotalSymbols(int totalSymbols) {
        this.totalSymbols = totalSymbols;
    }

    public int getBollingerBandsProcessed() {
        return bollingerBandsProcessed;
    }

    public void setBollingerBandsProcessed(int bollingerBandsProcessed) {
        this.bollingerBandsProcessed = bollingerBandsProcessed;
    }

    public int getBollingerBandsSuccessCount() {
        return bollingerBandsSuccessCount;
    }

    public void setBollingerBandsSuccessCount(int bollingerBandsSuccessCount) {
        this.bollingerBandsSuccessCount = bollingerBandsSuccessCount;
    }

    public int getBollingerBandsErrorCount() {
        return bollingerBandsErrorCount;
    }

    public void setBollingerBandsErrorCount(int bollingerBandsErrorCount) {
        this.bollingerBandsErrorCount = bollingerBandsErrorCount;
    }

    public int getDmiProcessed() {
        return dmiProcessed;
    }

    public void setDmiProcessed(int dmiProcessed) {
        this.dmiProcessed = dmiProcessed;
    }

    public int getDmiSuccessCount() {
        return dmiSuccessCount;
    }

    public void setDmiSuccessCount(int dmiSuccessCount) {
        this.dmiSuccessCount = dmiSuccessCount;
    }

    public int getDmiErrorCount() {
        return dmiErrorCount;
    }

    public void setDmiErrorCount(int dmiErrorCount) {
        this.dmiErrorCount = dmiErrorCount;
    }

    public int getTotalRecordsUpdated() {
        return totalRecordsUpdated;
    }

    public void setTotalRecordsUpdated(int totalRecordsUpdated) {
        this.totalRecordsUpdated = totalRecordsUpdated;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Object getBollingerBandsResults() {
        return bollingerBandsResults;
    }

    public void setBollingerBandsResults(Object bollingerBandsResults) {
        this.bollingerBandsResults = bollingerBandsResults;
    }

    public Object getDmiResults() {
        return dmiResults;
    }

    public void setDmiResults(Object dmiResults) {
        this.dmiResults = dmiResults;
    }

    @Override
    public String toString() {
        return "PositionsTechnicalIndicatorsResponse{" +
                "totalSymbols=" + totalSymbols +
                ", bollingerBandsProcessed=" + bollingerBandsProcessed +
                ", bollingerBandsSuccessCount=" + bollingerBandsSuccessCount +
                ", bollingerBandsErrorCount=" + bollingerBandsErrorCount +
                ", dmiProcessed=" + dmiProcessed +
                ", dmiSuccessCount=" + dmiSuccessCount +
                ", dmiErrorCount=" + dmiErrorCount +
                ", totalRecordsUpdated=" + totalRecordsUpdated +
                ", processingTimeMs=" + processingTimeMs +
                ", summary='" + summary + '\'' +
                '}';
    }
}
