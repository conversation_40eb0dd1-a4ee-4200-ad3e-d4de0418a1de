package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Request model for calculating technical indicators for position symbols.
 */
@Schema(description = "Request for calculating technical indicators for position symbols")
public class PositionsTechnicalIndicatorsRequest {

    @Schema(description = "Whether to perform a dry run (validation only) without actual updates",
            example = "false", defaultValue = "false")
    private boolean dryRun = false;

    @Schema(description = "Bollinger Bands calculation configuration")
    private BollingerBandsConfig bollingerBands;

    @Schema(description = "DMI calculation configuration")
    private DMIConfig dmi;

    /**
     * Configuration for Bollinger Bands calculation.
     */
    @Schema(description = "Bollinger Bands calculation configuration")
    public static class BollingerBandsConfig {
        @Schema(description = "Whether to enable Bollinger Bands calculation", defaultValue = "true")
        private boolean enabled = true;

        @Schema(description = "Number of periods for moving average calculation", example = "20", defaultValue = "20")
        private int period = 20;

        @Schema(description = "Standard deviation multiplier for upper and lower bands", example = "2.0", defaultValue = "2.0")
        private double stdDevMultiplier = 2.0;

        @Schema(description = "Calculation mode", example = "INCREMENTAL", defaultValue = "INCREMENTAL")
        private String calculationMode = "INCREMENTAL";

        // Default constructor
        public BollingerBandsConfig() {}

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getPeriod() {
            return period;
        }

        public void setPeriod(int period) {
            this.period = period;
        }

        public double getStdDevMultiplier() {
            return stdDevMultiplier;
        }

        public void setStdDevMultiplier(double stdDevMultiplier) {
            this.stdDevMultiplier = stdDevMultiplier;
        }

        public String getCalculationMode() {
            return calculationMode;
        }

        public void setCalculationMode(String calculationMode) {
            this.calculationMode = calculationMode;
        }
    }

    /**
     * Configuration for DMI calculation.
     */
    @Schema(description = "DMI calculation configuration")
    public static class DMIConfig {
        @Schema(description = "Whether to enable DMI calculation", defaultValue = "true")
        private boolean enabled = true;

        @Schema(description = "Number of periods for DMI calculation", example = "14", defaultValue = "14")
        private int period = 14;

        @Schema(description = "Calculation mode", example = "INCREMENTAL", defaultValue = "INCREMENTAL")
        private String calculationMode = "INCREMENTAL";

        @Schema(description = "Calculation method", example = "HYBRID_SQL_JAVA", defaultValue = "HYBRID_SQL_JAVA")
        private String calculationMethod = "HYBRID_SQL_JAVA";

        // Default constructor
        public DMIConfig() {}

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getPeriod() {
            return period;
        }

        public void setPeriod(int period) {
            this.period = period;
        }

        public String getCalculationMode() {
            return calculationMode;
        }

        public void setCalculationMode(String calculationMode) {
            this.calculationMode = calculationMode;
        }

        public String getCalculationMethod() {
            return calculationMethod;
        }

        public void setCalculationMethod(String calculationMethod) {
            this.calculationMethod = calculationMethod;
        }
    }

    // Default constructor
    public PositionsTechnicalIndicatorsRequest() {
        // Initialize with default configurations
        this.bollingerBands = new BollingerBandsConfig();
        this.dmi = new DMIConfig();
    }

    // Constructor with dry run parameter
    public PositionsTechnicalIndicatorsRequest(boolean dryRun) {
        this();
        this.dryRun = dryRun;
    }

    // Getters and setters
    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public BollingerBandsConfig getBollingerBands() {
        return bollingerBands;
    }

    public void setBollingerBands(BollingerBandsConfig bollingerBands) {
        this.bollingerBands = bollingerBands;
    }

    public DMIConfig getDmi() {
        return dmi;
    }

    public void setDmi(DMIConfig dmi) {
        this.dmi = dmi;
    }

    @Override
    public String toString() {
        return "PositionsTechnicalIndicatorsRequest{" +
                "dryRun=" + dryRun +
                ", bollingerBands=" + (bollingerBands != null ? 
                    "{enabled=" + bollingerBands.enabled + 
                    ", period=" + bollingerBands.period + 
                    ", stdDevMultiplier=" + bollingerBands.stdDevMultiplier + 
                    ", calculationMode='" + bollingerBands.calculationMode + "'}" : "null") +
                ", dmi=" + (dmi != null ? 
                    "{enabled=" + dmi.enabled + 
                    ", period=" + dmi.period + 
                    ", calculationMode='" + dmi.calculationMode + "'" + 
                    ", calculationMethod='" + dmi.calculationMethod + "'}" : "null") +
                '}';
    }
}
