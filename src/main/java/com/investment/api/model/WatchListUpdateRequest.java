package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Request model for updating OHLCV data for all watch list symbols.
 */
@Schema(description = "Request parameters for updating OHLCV data for watch list symbols")
public class WatchListUpdateRequest {

    @Schema(description = "Whether to recalculate technical indicators after OHLCV update",
            example = "false")
    private boolean recalculateTechnicalIndicators = false;

    @Schema(description = "If true, only validate and report without updating data", 
            example = "false")
    private boolean dryRun = false;

    // Default constructor
    public WatchListUpdateRequest() {}

    // Constructor with parameters
    public WatchListUpdateRequest(boolean recalculateTechnicalIndicators, boolean dryRun) {
        this.recalculateTechnicalIndicators = recalculateTechnicalIndicators;
        this.dryRun = dryRun;
    }

    // Getters and setters
    public boolean isRecalculateTechnicalIndicators() {
        return recalculateTechnicalIndicators;
    }

    public void setRecalculateTechnicalIndicators(boolean recalculateTechnicalIndicators) {
        this.recalculateTechnicalIndicators = recalculateTechnicalIndicators;
    }

    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    @Override
    public String toString() {
        return "WatchListUpdateRequest{" +
                "recalculateTechnicalIndicators=" + recalculateTechnicalIndicators +
                ", dryRun=" + dryRun +
                '}';
    }
}
