package com.investment.api.controller;

import com.investment.api.model.*;
import com.investment.process.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for process management operations.
 * Provides endpoints for monitoring and controlling long-running processes.
 */
@RestController
@RequestMapping("/api/processes")
@Tag(name = "Process Management", description = "Monitor and control long-running operations")
@Log4j2
public class ProcessController {

    private final ProcessManager processManager;
    
    public ProcessController(ProcessManager processManager) {
        this.processManager = processManager;
    }
    
    /**
     * Abort all currently active processes.
     */
    @PostMapping("/abort-all")
    @Operation(
        summary = "Abort all active processes",
        description = "Requests cancellation of all currently running processes. " +
                     "Processes will attempt to clean up gracefully and may take some time to fully terminate. " +
                     "This operation is useful for emergency situations or system maintenance."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Abort requests sent successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error during abort operation"
        )
    })
    public ResponseEntity<ApiResponse<AbortAllProcessesResponse>> abortAllProcesses() {
        try {
            log.info("Received request to abort all active processes");
            
            ProcessManager.AbortAllResult result = processManager.abortAllProcesses();
            AbortAllProcessesResponse response = new AbortAllProcessesResponse(result);
            
            log.info("Abort all processes completed: {}", response.getSummary());
            
            if (result.isFullSuccess()) {
                return ResponseEntity.ok(ApiResponse.success(
                    "All active processes abort requested successfully", response));
            } else {
                return ResponseEntity.ok(ApiResponse.success(
                    "Abort requests sent with some failures", response));
            }
            
        } catch (Exception e) {
            log.error("Error during abort all processes operation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to abort processes: " + e.getMessage()));
        }
    }
    
    /**
     * Get all processes (active and completed).
     */
    @GetMapping
    @Operation(
        summary = "Get all processes",
        description = "Retrieve information about all processes in the system, including active and completed ones."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Processes retrieved successfully"
        )
    })
    public ResponseEntity<ApiResponse<List<ProcessInfoResponse>>> getAllProcesses(
            @Parameter(description = "Filter by process status")
            @RequestParam(required = false) ProcessStatus status,
            @Parameter(description = "Filter by process type")
            @RequestParam(required = false) ProcessType type) {
        
        try {
            List<ProcessInfo> processes;
            
            if (status != null && type != null) {
                // Filter by both status and type
                processes = processManager.getAllProcesses().stream()
                        .filter(p -> p.getStatus() == status && p.getProcessType() == type)
                        .collect(Collectors.toList());
            } else if (status != null) {
                processes = processManager.getProcessesByStatus(status);
            } else if (type != null) {
                processes = processManager.getProcessesByType(type);
            } else {
                processes = processManager.getAllProcesses();
            }
            
            List<ProcessInfoResponse> response = processes.stream()
                    .map(ProcessInfoResponse::new)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d processes", response.size()), response));
            
        } catch (Exception e) {
            log.error("Error retrieving processes", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve processes: " + e.getMessage()));
        }
    }
    
    /**
     * Get only active processes.
     */
    @GetMapping("/active")
    @Operation(
        summary = "Get active processes",
        description = "Retrieve information about currently running processes only."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Active processes retrieved successfully"
        )
    })
    public ResponseEntity<ApiResponse<List<ProcessInfoResponse>>> getActiveProcesses() {
        try {
            List<ProcessInfo> activeProcesses = processManager.getActiveProcesses();
            List<ProcessInfoResponse> response = activeProcesses.stream()
                    .map(ProcessInfoResponse::new)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d active processes", response.size()), response));
            
        } catch (Exception e) {
            log.error("Error retrieving active processes", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve active processes: " + e.getMessage()));
        }
    }
    
    /**
     * Get a specific process by ID.
     */
    @GetMapping("/{processId}")
    @Operation(
        summary = "Get process by ID",
        description = "Retrieve detailed information about a specific process."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Process found and retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Process not found"
        )
    })
    public ResponseEntity<ApiResponse<ProcessInfoResponse>> getProcess(
            @Parameter(description = "Process ID", example = "dmi_calculation_202412151430_0001")
            @PathVariable String processId) {
        
        try {
            Optional<ProcessContext> processContext = processManager.getProcess(processId);
            
            if (processContext.isPresent()) {
                ProcessInfoResponse response = new ProcessInfoResponse(processContext.get().getProcessInfo());
                return ResponseEntity.ok(ApiResponse.success("Process found", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Process not found: " + processId));
            }
            
        } catch (Exception e) {
            log.error("Error retrieving process: {}", processId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve process: " + e.getMessage()));
        }
    }
    
    /**
     * Abort a specific process.
     */
    @PostMapping("/{processId}/abort")
    @Operation(
        summary = "Abort a specific process",
        description = "Request cancellation of a specific process. The process will attempt to clean up gracefully."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Abort request sent successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "Process not found or already terminated"
        )
    })
    public ResponseEntity<ApiResponse<ProcessInfoResponse>> abortProcess(
            @Parameter(description = "Process ID", example = "dmi_calculation_202412151430_0001")
            @PathVariable String processId) {
        
        try {
            boolean aborted = processManager.abortProcess(processId);
            
            if (aborted) {
                Optional<ProcessContext> processContext = processManager.getProcess(processId);
                if (processContext.isPresent()) {
                    ProcessInfoResponse response = new ProcessInfoResponse(processContext.get().getProcessInfo());
                    return ResponseEntity.ok(ApiResponse.success("Process abort requested", response));
                }
            }
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Process not found or already terminated: " + processId));
            
        } catch (Exception e) {
            log.error("Error aborting process: {}", processId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to abort process: " + e.getMessage()));
        }
    }
    
    /**
     * Get process statistics.
     */
    @GetMapping("/statistics")
    @Operation(
        summary = "Get process statistics",
        description = "Retrieve statistics about processes in the system, including counts by status and type."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Statistics retrieved successfully"
        )
    })
    public ResponseEntity<ApiResponse<ProcessStatisticsResponse>> getStatistics() {
        try {
            ProcessManager.ProcessStatistics statistics = processManager.getStatistics();
            ProcessStatisticsResponse response = new ProcessStatisticsResponse(statistics);
            
            return ResponseEntity.ok(ApiResponse.success("Process statistics", response));
            
        } catch (Exception e) {
            log.error("Error retrieving process statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve statistics: " + e.getMessage()));
        }
    }
    
    /**
     * Clean up completed processes.
     */
    @PostMapping("/cleanup")
    @Operation(
        summary = "Clean up completed processes",
        description = "Remove completed, failed, and aborted processes from memory to free up resources."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Cleanup completed successfully"
        )
    })
    public ResponseEntity<ApiResponse<String>> cleanupProcesses() {
        try {
            int cleanedUp = processManager.cleanupCompletedProcesses();
            String message = String.format("Cleaned up %d completed processes", cleanedUp);
            
            return ResponseEntity.ok(ApiResponse.success(message, message));
            
        } catch (Exception e) {
            log.error("Error during process cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to cleanup processes: " + e.getMessage()));
        }
    }
}
