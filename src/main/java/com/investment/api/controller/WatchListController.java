package com.investment.api.controller;

import com.investment.api.model.*;
import com.investment.model.WatchListItem;
import com.investment.service.WatchListService;
import com.investment.service.TechnicalSignalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for watch list operations.
 */
@RestController
@RequestMapping("/api/watchlist")
@Tag(name = "Watch List", description = "Watch list management operations")
@Log4j2
public class WatchListController   {

    private final WatchListService watchListService;
    private final TechnicalSignalService technicalSignalService;

    public WatchListController(WatchListService watchListService, TechnicalSignalService technicalSignalService) {
        this.watchListService = watchListService;
        this.technicalSignalService = technicalSignalService;
    }

    @PostMapping
    @Operation(summary = "Add symbol to watch list", description = "Create a new watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Watch list item created successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "409", description = "Symbol already exists in watch list"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> createWatchListItem(
            @Valid @RequestBody CreateWatchListRequest request) {
        
        try {
            log.info("Creating watch list item: {}", request);
            
            WatchListItem item = watchListService.createWatchListItem(request);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);
            
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Watch list item created successfully", response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for creating watch list item: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            log.error("Database error creating watch list item", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create watch list item: " + e.getMessage()));
        }
    }

    @GetMapping
    @Operation(summary = "Get all watch list items", description = "Retrieve all watch list items ordered by display index")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list items retrieved successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<List<WatchListResponse>>> getAllWatchListItems() {
        try {
            log.debug("Retrieving all watch list items");
            
            List<WatchListItem> items = watchListService.getAllWatchListItems();
            List<WatchListResponse> responses = items.stream()
                    .map(WatchListResponse::fromWatchListItem)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(ApiResponse.success("Retrieved " + responses.size() + " watch list items", responses));

        } catch (SQLException e) {
            log.error("Database error retrieving watch list items", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve watch list items: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get watch list item by ID", description = "Retrieve a specific watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> getWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id) {
        
        try {
            log.debug("Retrieving watch list item by ID: {}", id);
            
            Optional<WatchListItem> item = watchListService.getWatchListItemById(id);
            
            if (item.isPresent()) {
                WatchListResponse response = WatchListResponse.fromWatchListItem(item.get());
                return ResponseEntity.ok(ApiResponse.success("Watch list item found", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Watch list item not found: " + id));
            }

        } catch (SQLException e) {
            log.error("Database error retrieving watch list item by ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update watch list item", description = "Update an existing watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> updateWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id,
            @Valid @RequestBody UpdateWatchListRequest request) {
        
        try {
            log.info("Updating watch list item ID: {} with request: {}", id, request);
            
            if (!request.hasUpdates()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("No update fields provided"));
            }

            WatchListItem item = watchListService.updateWatchListItem(id, request);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);

            return ResponseEntity.ok(ApiResponse.success("Watch list item updated successfully", response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for updating watch list item ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            log.error("Database error updating watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/performance")
    @Operation(summary = "Update performance metrics", description = "Update performance metrics for a watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Performance metrics updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListResponse>> updateWatchListPerformance(
            @Parameter(description = "Watch list item ID") @PathVariable Long id,
            @Parameter(description = "1-month performance percentage") @RequestParam(required = false) BigDecimal oneMonthPerf,
            @Parameter(description = "3-month performance percentage") @RequestParam(required = false) BigDecimal threeMonthPerf,
            @Parameter(description = "6-month performance percentage") @RequestParam(required = false) BigDecimal sixMonthPerf) {
        
        try {
            log.info("Updating performance for watch list item ID: {}", id);
            
            WatchListItem item = watchListService.updateWatchListPerformance(id, oneMonthPerf, threeMonthPerf, sixMonthPerf);
            WatchListResponse response = WatchListResponse.fromWatchListItem(item);
            
            return ResponseEntity.ok(ApiResponse.success("Performance metrics updated successfully", response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for updating performance for watch list item ID {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            log.error("Database error updating performance for watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update performance metrics: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Remove from watch list", description = "Delete a watch list item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list item deleted successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Watch list item not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<String>> deleteWatchListItem(
            @Parameter(description = "Watch list item ID") @PathVariable Long id) {
        
        try {
            log.info("Deleting watch list item ID: {}", id);
            
            boolean deleted = watchListService.deleteWatchListItem(id);
            
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("Watch list item deleted successfully", "Watch list item " + id + " deleted"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Watch list item not found: " + id));
            }

        } catch (SQLException e) {
            log.error("Database error deleting watch list item ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete watch list item: " + e.getMessage()));
        }
    }

    @PutMapping("/reorder")
    @Operation(summary = "Bulk reorder watch list items", description = "Update display indexes for multiple watch list items")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Watch list items reordered successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<String>> reorderWatchListItems(
            @Valid @RequestBody ReorderWatchListRequest request) {
        
        try {
            log.info("Reordering watch list items: {}", request);
            
            request.validate();
            watchListService.reorderWatchListItems(request.getIdToIndexMap());
            
            return ResponseEntity.ok(ApiResponse.success("Watch list items reordered successfully",
                    "Reordered " + request.getIdToIndexMap().size() + " items"));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for reordering watch list items: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));

        } catch (SQLException e) {
            log.error("Database error reordering watch list items", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to reorder watch list items: " + e.getMessage()));
        }
    }

    @PostMapping("/recalculate-performance")
    @Operation(summary = "Recalculate performance metrics", description = "Recalculate performance metrics for all watch list items using current OHLCV data")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Performance metrics recalculated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<RecalculatePerformanceResponse>> recalculatePerformance() {

        try {
            log.info("Starting performance recalculation for all watch list items");

            RecalculatePerformanceResponse response = watchListService.calculateAndUpdateAllPerformance();

            return ResponseEntity.ok(ApiResponse.success(
                    "Performance recalculation completed: " + response.getSummaryMessage(),
                    response));

        } catch (SQLException e) {
            log.error("Database error during performance recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to recalculate performance metrics: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during performance recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error during performance recalculation: " + e.getMessage()));
        }
    }

    @PostMapping("/update-ohlcv-data")
    @Operation(summary = "Update OHLCV data for all watch list symbols",
               description = "Updates OHLCV historical data for all symbols in the watch list and optionally recalculates technical indicators. " +
                           "This is a comprehensive update operation that includes: " +
                           "1) OHLCV data updates from Yahoo Finance for all watch list symbols " +
                           "2) Bollinger Bands recalculation using INCREMENTAL mode (if enabled) " +
                           "3) DMI indicators recalculation using INCREMENTAL mode (if enabled). " +
                           "The operation may take several minutes depending on the number of symbols.")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "OHLCV data update completed successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListUpdateResponse>> updateOHLCVDataForWatchList(
            @Valid @RequestBody WatchListUpdateRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {

        try {
            log.info("Starting OHLCV data update for watch list symbols: {}", request);

            // Log warning for actual update operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL OHLCV UPDATE FOR WATCH LIST - This will download data from external APIs and may take significant time (up to 30+ minutes for DMI calculations)");
            }

            // Check if client connection is still active before starting long operation
            if (httpRequest.isAsyncStarted() || !httpResponse.isCommitted()) {
                log.debug("Client connection verified before starting long-running operation");
            }

            WatchListUpdateResponse response = watchListService.updateOHLCVDataForWatchList(request);

            // Check connection status before sending response
            try {
                if (httpResponse.isCommitted()) {
                    log.warn("Response already committed - client may have disconnected during processing");
                    return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                            .body(ApiResponse.error("Client connection lost during processing, but operation completed successfully"));
                }
            } catch (Exception connectionCheck) {
                log.warn("Unable to check connection status: {}", connectionCheck.getMessage());
            }

            String operation = request.isDryRun() ? "OHLCV update validation" : "OHLCV update";
            log.info("{} completed for watch list: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed for watch list",
                    response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for watch list OHLCV update: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            log.error("Database error during watch list OHLCV update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            // Enhanced error handling for connection issues
            String errorMessage = e.getMessage();
            if (errorMessage != null && (errorMessage.contains("ClientAbortException") ||
                                       errorMessage.contains("connection was aborted") ||
                                       errorMessage.contains("Broken pipe"))) {
                log.warn("Client disconnected during long-running operation: {}", errorMessage);
                return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                        .body(ApiResponse.error("Client connection lost during processing. The operation may have completed successfully - please refresh to see updated data."));
            }

            log.error("Unexpected error during watch list OHLCV update", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    @PostMapping("/recalculate-technical-signals")
    @Operation(summary = "Recalculate technical signal streaks",
               description = "Recalculate BUY signal indicator streaks (Bullish Bollinger, DMI Bullish, Combined Signal) for all watch list items")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Technical signals recalculated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<RecalculatePerformanceResponse>> recalculateTechnicalSignals() {

        try {
            log.info("Starting technical signal recalculation for all watch list items");

            List<WatchListItem> watchListItems = watchListService.getAllWatchListItems();

            int totalItems = watchListItems.size();
            int successfulUpdates = 0;
            int failedUpdates = 0;
            List<String> successfulSymbols = new ArrayList<>();
            List<String> failedSymbols = new ArrayList<>();

            long startTime = System.currentTimeMillis();

            for (WatchListItem item : watchListItems) {
                try {
                    technicalSignalService.updateTechnicalSignalStreaks(item.getId(), item.getSymbol());
                    successfulUpdates++;
                    successfulSymbols.add(item.getSymbol());
                    log.debug("Successfully updated technical signals for symbol: {}", item.getSymbol());
                } catch (Exception e) {
                    failedUpdates++;
                    failedSymbols.add(item.getSymbol());
                    log.warn("Failed to update technical signals for symbol: {}", item.getSymbol(), e);
                }
            }

            long processingTime = System.currentTimeMillis() - startTime;

            RecalculatePerformanceResponse response = new RecalculatePerformanceResponse();
            response.setTotalItems(totalItems);
            response.setSuccessfulUpdates(successfulUpdates);
            response.setFailedUpdates(failedUpdates);
            response.setSkippedItems(0);
            response.setSuccessfulSymbols(successfulSymbols);
            response.setFailedSymbols(failedSymbols);
            response.setSkippedSymbols(new ArrayList<>());
            response.setProcessingTimeMs(processingTime);

            return ResponseEntity.ok(ApiResponse.success(
                    "Technical signal recalculation completed: " + response.getSummaryMessage(),
                    response));

        } catch (SQLException e) {
            log.error("Database error during technical signal recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to recalculate technical signals: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during technical signal recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error during technical signal recalculation: " + e.getMessage()));
        }
    }

    @PostMapping("/recalculate-bollinger-bands")
    @Operation(summary = "Recalculate Bollinger Bands for all watch list symbols",
               description = "Recalculates Bollinger Band technical indicators for all symbols in the watch list using INCREMENTAL mode. " +
                           "This operation focuses only on Bollinger Bands calculation and does not update OHLCV data. " +
                           "Make sure OHLCV data is up-to-date before running this operation for accurate results.")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Bollinger Bands recalculation completed successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListUpdateResponse>> recalculateBollingerBandsForWatchList(
            @Valid @RequestBody WatchListUpdateRequest request) {

        try {
            log.info("Starting Bollinger Bands recalculation for watch list symbols: {}", request);

            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL BOLLINGER BANDS RECALCULATION FOR WATCH LIST - This will update database records with technical indicator data");
            }

            WatchListUpdateResponse response = watchListService.recalculateBollingerBandsForWatchList(request);

            String operation = request.isDryRun() ? "Bollinger Bands recalculation validation" : "Bollinger Bands recalculation";
            log.info("{} completed for watch list: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed for watch list",
                    response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for watch list Bollinger Bands recalculation: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            log.error("Database error during watch list Bollinger Bands recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during watch list Bollinger Bands recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    @PostMapping("/recalculate-dmi")
    @Operation(summary = "Recalculate DMI indicators for all watch list symbols",
               description = "Recalculates DMI (Directional Movement Index) technical indicators for all symbols in the watch list using INCREMENTAL mode. " +
                           "This operation focuses only on DMI calculation and does not update OHLCV data. " +
                           "Make sure OHLCV data is up-to-date before running this operation for accurate results. " +
                           "DMI calculations can take several minutes depending on the number of symbols.")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "DMI recalculation completed successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponse<WatchListUpdateResponse>> recalculateDMIForWatchList(
            @Valid @RequestBody WatchListUpdateRequest request) {

        try {
            log.info("Starting DMI recalculation for watch list symbols: {}", request);

            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL DMI RECALCULATION FOR WATCH LIST - This will update database records with technical indicator data");
            }

            WatchListUpdateResponse response = watchListService.recalculateDMIForWatchList(request);

            String operation = request.isDryRun() ? "DMI recalculation validation" : "DMI recalculation";
            log.info("{} completed for watch list: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed for watch list",
                    response));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for watch list DMI recalculation: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid request: " + e.getMessage()));
        } catch (SQLException e) {
            log.error("Database error during watch list DMI recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Database error: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during watch list DMI recalculation", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Unexpected error: " + e.getMessage()));
        }
    }

    /**
     * Upload and process a CSV file containing TradingView StockScreener data for watch list.
     * This endpoint allows uploading a CSV file with TradingView StockScreener data and importing it into the watch list.
     *
     * @param file The CSV file to upload
     * @param dryRun Whether to perform a dry run (no actual insertions)
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates Whether to skip instruments that already exist in watch list
     * @param validateData Whether to perform data validation
     * @return CSV processing results with summary
     */
    @PostMapping(value = "/upload-us-tradingview-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload and process TradingView StockScreener CSV file for watch list",
               description = "Uploads a CSV file containing TradingView StockScreener data and imports it into the watch list. " +
                           "The CSV file must have headers: Symbol, Description, Price, Price - Currency, Price Change % 1 day, " +
                           "Volume 1 day, Relative Volume 1 day, Market capitalization, Market capitalization - Currency, " +
                           "Sector, Analyst Rating, Performance % 1 month, Performance % 3 months, Performance % 6 months. " +
                           "Only extracts Symbol and Performance % columns (1/3/6 months) for watch list entries. " +
                           "Skips symbols already in watch list and symbols not found in instruments table. " +
                           "Can perform actual import (dryRun=false) or just validate and report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add new entries to the watch list.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "TradingView CSV processing completed successfully",
            content = @Content(schema = @Schema(implementation = CsvUploadResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid file or request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error during processing"
        )
    })
    public ResponseEntity<ApiResponse<CsvUploadResponse>> uploadUsTradingViewCsv(
            @Parameter(description = "CSV file containing TradingView StockScreener data", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Perform dry run without actual database changes")
            @RequestParam(defaultValue = "true") boolean dryRun,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "10000") int maxInstruments,
            @Parameter(description = "Skip symbols that already exist in watch list")
            @RequestParam(defaultValue = "true") boolean skipDuplicates,
            @Parameter(description = "Perform data validation on CSV content")
            @RequestParam(defaultValue = "true") boolean validateData) {

        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("File is required and cannot be empty"));
            }

            log.info("Starting TradingView CSV upload processing - file: {}, size: {} bytes, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                       file.getOriginalFilename(), file.getSize(), dryRun, maxInstruments, skipDuplicates, validateData);

            // Log warning for actual import operations
            if (!dryRun) {
                log.warn("PERFORMING ACTUAL TRADINGVIEW CSV IMPORT - This will add new entries to the watch list");
            }

            CsvUploadResponse response = watchListService.processTradingViewCsvFile(
                    file, dryRun, maxInstruments, skipDuplicates, validateData);

            String operation = dryRun ? "TradingView CSV validation" : "TradingView CSV import";
            log.info("{} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed",
                    response
            ));

        } catch (Exception e) {
            log.error("Error during TradingView CSV processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("TradingView CSV processing failed: " + e.getMessage()));
        }
    }

    /**
     * Upload and process a CSV file containing TradingView StockScreener data for Hong Kong stocks in watch list.
     * This endpoint allows uploading a CSV file with TradingView StockScreener data for Hong Kong stocks and importing it into the watch list.
     *
     * @param file The CSV file to upload
     * @param dryRun Whether to perform a dry run (no actual insertions)
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates Whether to skip instruments that already exist in watch list
     * @param validateData Whether to perform data validation
     * @return CSV processing results with summary
     */
    @PostMapping(value = "/upload-hk-tradingview-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload and process TradingView StockScreener CSV file for Hong Kong stocks in watch list",
               description = "Uploads a CSV file containing TradingView StockScreener data for Hong Kong stocks and imports it into the watch list. " +
                           "The CSV file must have headers: Symbol, Description, Price, Price - Currency, Price Change % 1 day, " +
                           "Volume 1 day, Relative Volume 1 day, Market capitalization, Market capitalization - Currency, " +
                           "Sector, Analyst Rating, Performance % 1 month, Performance % 3 months, Performance % 6 months. " +
                           "Only extracts Symbol and Performance % columns (1/3/6 months) for watch list entries. " +
                           "Converts numeric symbols to Hong Kong format (e.g., '2282' → '2282.HK', '136' → '0136.HK'). " +
                           "Skips symbols already in watch list and symbols not found in instruments table. " +
                           "Can perform actual import (dryRun=false) or just validate and report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add new entries to the watch list.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Hong Kong TradingView CSV processing completed successfully",
            content = @Content(schema = @Schema(implementation = CsvUploadResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid file or request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error during processing"
        )
    })
    public ResponseEntity<ApiResponse<CsvUploadResponse>> uploadHkTradingViewCsv(
            @Parameter(description = "CSV file containing TradingView StockScreener data for Hong Kong stocks", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Perform dry run without actual database changes")
            @RequestParam(defaultValue = "true") boolean dryRun,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "10000") int maxInstruments,
            @Parameter(description = "Skip symbols that already exist in watch list")
            @RequestParam(defaultValue = "true") boolean skipDuplicates,
            @Parameter(description = "Perform data validation on CSV content")
            @RequestParam(defaultValue = "true") boolean validateData) {

        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("File is required and cannot be empty"));
            }

            log.info("Starting Hong Kong TradingView CSV upload processing - file: {}, size: {} bytes, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                       file.getOriginalFilename(), file.getSize(), dryRun, maxInstruments, skipDuplicates, validateData);

            // Log warning for actual import operations
            if (!dryRun) {
                log.warn("PERFORMING ACTUAL HONG KONG TRADINGVIEW CSV IMPORT - This will add new entries to the watch list");
            }

            CsvUploadResponse response = watchListService.processHkTradingViewCsvFile(
                    file, dryRun, maxInstruments, skipDuplicates, validateData);

            String operation = dryRun ? "Hong Kong TradingView CSV validation" : "Hong Kong TradingView CSV import";
            log.info("{} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed",
                    response
            ));

        } catch (Exception e) {
            log.error("Error during Hong Kong TradingView CSV processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Hong Kong TradingView CSV processing failed: " + e.getMessage()));
        }
    }
}
