package com.investment.api.controller;

import com.investment.api.model.ApiResponse;
import com.investment.api.model.*;
import com.investment.process.AsyncProcessExecutor;
import com.investment.process.ProcessManager;
import com.investment.service.BollingerBandService;
import com.investment.service.DMIService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * REST controller for technical indicator calculations.
 */
@RestController
@RequestMapping("/api/technical-indicators")
@Tag(name = "Technical Indicators", description = "API for calculating and managing technical indicators")
@Log4j2
public class TechnicalIndicatorController   {

    private final BollingerBandService bollingerBandService;
    private final DMIService dmiService;
    private final ProcessManager processManager;

    public TechnicalIndicatorController(BollingerBandService bollingerBandService, DMIService dmiService, ProcessManager processManager) {
        this.bollingerBandService = bollingerBandService;
        this.dmiService = dmiService;
        this.processManager = processManager;
    }
    
    /**
     * Calculate and update Bollinger Band technical indicators for all symbols.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    @PostMapping("/bollinger-bands/calculate")
    @Operation(
        summary = "Calculate Bollinger Band indicators for symbols",
        description = "Calculates and updates Bollinger Band technical indicators (middle band, standard deviation, upper band, lower band) " +
                     "for symbols in the OHLCV database. Supports two modes: " +
                     "1) Specific symbols: Provide a 'symbols' array to calculate for specific stocks " +
                     "2) Pagination: Use 'startIndex', 'endIndex', and 'maxSymbols' to process a range of symbols " +
                     "When 'symbols' is provided, pagination parameters are ignored. " +
                     "Uses optimized SQL window functions for efficient bulk calculation. " +
                     "Supports dry-run mode for validation, custom periods and standard deviation multipliers, " +
                     "and filtering based on minimum data requirements."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Bollinger Band calculation completed successfully",
            content = @Content(schema = @Schema(implementation = BollingerBandResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500", 
            description = "Internal server error during calculation"
        )
    })
    public ResponseEntity<ApiResponse<BollingerBandResponse>> calculateBollingerBands(
            @Valid @RequestBody BollingerBandRequest request) {
        
        try {
            log.info("Received Bollinger Band calculation request: {}", request);
            
            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL BOLLINGER BAND CALCULATION - This will update database records with technical indicator data");
            }
            
            BollingerBandResponse response = bollingerBandService.calculateBollingerBands(request);
            
            String operation = request.isDryRun() ? "Bollinger Band calculation validation" : "Bollinger Band calculation";
            log.info("{} completed: {}", operation, response.getSummary());
            
            // Determine response message based on status
            String message;
            switch (response.getStatus()) {
                case "success":
                    message = operation + " completed successfully";
                    break;
                case "partial_success":
                    message = operation + " completed with some failures";
                    break;
                case "failed":
                    message = operation + " failed";
                    break;
                default:
                    message = operation + " completed";
            }
            
            return ResponseEntity.ok(ApiResponse.success(message, response));
            
        } catch (Exception e) {
            log.error("Error during Bollinger Band calculation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to calculate Bollinger Bands: " + e.getMessage()));
        }
    }

    /**
     * Calculate and update DMI (Directional Movement Index) technical indicators for all symbols.
     *
     * @param request The calculation request parameters
     * @return Response containing calculation results and statistics
     */
    @PostMapping("/dmi/calculate")
    @Operation(
        summary = "Calculate DMI indicators for symbols",
        description = "Calculates and updates DMI (Directional Movement Index) technical indicators (+DI, -DI, ADX) " +
                     "for symbols in the OHLCV database. Supports two modes: " +
                     "1) Specific symbols: Provide a 'symbols' array to calculate for specific stocks " +
                     "2) Pagination: Use 'startIndex', 'endIndex', and 'maxSymbols' to process a range of symbols " +
                     "When 'symbols' is provided, pagination parameters are ignored. " +
                     "DMI is a momentum indicator that measures the strength of price movement in positive and negative directions. " +
                     "Supports incremental calculation, dry-run mode for validation, custom periods, " +
                     "and filtering based on minimum data requirements."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "DMI calculation completed successfully",
            content = @Content(schema = @Schema(implementation = DMIResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error during calculation"
        )
    })
    public ResponseEntity<ApiResponse<DMIResponse>> calculateDMI(
            @Valid @RequestBody DMIRequest request) {

        try {
            log.info("Received DMI calculation request: {}", request);

            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL DMI CALCULATION - This will update database records with technical indicator data");
            }

            DMIResponse response = dmiService.calculateDMI(request);

            String operation = request.isDryRun() ? "DMI calculation validation" : "DMI calculation";
            log.info("{} completed: {}", operation, response.getSummary());

            // Determine response message based on status
            String message;
            switch (response.getStatus()) {
                case "success":
                    message = operation + " completed successfully";
                    break;
                case "partial_success":
                    message = operation + " completed with some failures";
                    break;
                case "failed":
                    message = operation + " failed";
                    break;
                default:
                    message = operation + " completed";
            }

            return ResponseEntity.ok(ApiResponse.success(message, response));

        } catch (Exception e) {
            log.error("Error during DMI calculation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to calculate DMI: " + e.getMessage()));
        }
    }

    /**
     * Calculate DMI asynchronously with process tracking.
     * This endpoint is recommended for large datasets that may take significant time.
     */
    @PostMapping("/dmi/calculate-async")
    @Operation(
        summary = "Calculate DMI asynchronously with process tracking",
        description = "Calculate Directional Movement Index (DMI) technical indicators for instruments asynchronously. " +
                     "This endpoint starts the calculation in the background and returns immediately with process information. " +
                     "Use the process management endpoints to monitor progress and control execution. " +
                     "Recommended for large datasets that may take significant time to process."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "202",
            description = "DMI calculation started successfully",
            content = @Content(schema = @Schema(implementation = ProcessInfoResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error during calculation startup"
        )
    })
    public ResponseEntity<ApiResponse<ProcessInfoResponse>> calculateDMIAsync(
            @Valid @RequestBody DMIRequest request,
            @Parameter(description = "Who initiated the process (for tracking)")
            @RequestParam(defaultValue = "api-user") String initiatedBy) {

        try {
            log.info("Received async DMI calculation request: {}", request);

            // Log warning for actual calculation operations
            if (!request.isDryRun()) {
                log.warn("PERFORMING ACTUAL ASYNC DMI CALCULATION - This will update database records with technical indicator data");
            }

            // Start the async calculation and get the process info
            String processId = dmiService.startDMICalculationAsync(request, initiatedBy);

            // Get the process info from the process manager
            ProcessInfoResponse processInfo = processManager.getProcess(processId)
                    .map(context -> new ProcessInfoResponse(context.getProcessInfo()))
                    .orElseThrow(() -> new RuntimeException("Failed to retrieve process information"));

            String operation = request.isDryRun() ? "DMI calculation validation" : "DMI calculation";
            log.info("Async {} started with process ID: {}", operation, processInfo.getProcessId());

            return ResponseEntity.accepted().body(ApiResponse.success(
                    "Async " + operation + " started successfully", processInfo));

        } catch (Exception e) {
            log.error("Error starting async DMI calculation", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to start async DMI calculation: " + e.getMessage()));
        }
    }

    /**
     * Get information about available technical indicators.
     *
     * @return Information about supported technical indicators
     */
    @GetMapping("/info")
    @Operation(
        summary = "Get information about available technical indicators",
        description = "Returns information about the technical indicators supported by this API, " +
                     "including their parameters and calculation methods."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "Technical indicators information retrieved successfully"
        )
    })
    public ResponseEntity<ApiResponse<TechnicalIndicatorInfo>> getTechnicalIndicatorInfo() {
        try {
            TechnicalIndicatorInfo info = new TechnicalIndicatorInfo();
            return ResponseEntity.ok(ApiResponse.success("Technical indicators information", info));
        } catch (Exception e) {
            log.error("Error retrieving technical indicator information", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("Failed to retrieve technical indicator information: " + e.getMessage()));
        }
    }
    
    /**
     * Information about available technical indicators.
     */
    @Schema(description = "Information about available technical indicators")
    public static class TechnicalIndicatorInfo {
        
        @Schema(description = "List of available technical indicators")
        private final IndicatorInfo[] indicators = {
            new IndicatorInfo(
                "bollinger_bands",
                "Bollinger Bands",
                "Volatility indicator consisting of a middle band (SMA) and upper/lower bands based on standard deviation",
                new String[]{"period", "stdDevMultiplier", "minDataPoints", "startIndex", "endIndex", "maxSymbols", "symbols"},
                "POST /api/technical-indicators/bollinger-bands/calculate"
            ),
            new IndicatorInfo(
                "dmi",
                "Directional Movement Index (DMI)",
                "Momentum indicator that measures the strength of price movement in positive and negative directions, includes +DI, -DI, and ADX",
                new String[]{"period", "minDataPoints", "startIndex", "endIndex", "maxSymbols", "symbols"},
                "POST /api/technical-indicators/dmi/calculate"
            )
        };
        
        public IndicatorInfo[] getIndicators() {
            return indicators;
        }
        
        @Schema(description = "Information about a specific technical indicator")
        public static class IndicatorInfo {
            @Schema(description = "Indicator identifier", example = "bollinger_bands")
            private final String id;
            
            @Schema(description = "Human-readable name", example = "Bollinger Bands")
            private final String name;
            
            @Schema(description = "Description of the indicator")
            private final String description;
            
            @Schema(description = "Available parameters for configuration")
            private final String[] parameters;
            
            @Schema(description = "API endpoint for calculation", example = "POST /api/technical-indicators/bollinger-bands/calculate")
            private final String endpoint;
            
            public IndicatorInfo(String id, String name, String description, String[] parameters, String endpoint) {
                this.id = id;
                this.name = name;
                this.description = description;
                this.parameters = parameters;
                this.endpoint = endpoint;
            }
            
            public String getId() { return id; }
            public String getName() { return name; }
            public String getDescription() { return description; }
            public String[] getParameters() { return parameters; }
            public String getEndpoint() { return endpoint; }
        }
    }
}
