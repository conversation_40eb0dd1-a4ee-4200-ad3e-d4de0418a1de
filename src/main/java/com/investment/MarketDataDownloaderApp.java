package com.investment;

import com.investment.config.MarketDataDownloadConfig;
import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import com.investment.provider.DataProvider;
import com.investment.provider.YahooFinanceProvider;

import lombok.extern.log4j.Log4j2;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Log4j2
public class MarketDataDownloaderApp   {

    private static final DatabaseManager dbManager = new DatabaseManager();

    // Yahoo Finance’s historical data typically starts from January 2, 1962
    public static final int YAHOO_HISTORICAL_DATA_START_YEAR = 1962;
    // Calculate the difference between current year and Yahoo's earliest data year
    public static final int DEFAULT_HISTORY_YEARS = LocalDate.now().getYear() - YAHOO_HISTORICAL_DATA_START_YEAR - 1;

    public static void main(String[] args) {
        MarketDataDownloadConfig config = parseCommandLineArgs(args);

        try {
            // Initialize database
            dbManager.initDatabase();

            // Automatically discover all instruments from database
            List<Instrument> instruments = dbManager.getAllInstruments();

            if (instruments.isEmpty()) {
                log.warn("No instruments found in database. Please add instruments first using the SEC synchronization feature or manually.");
                return;
            }

            log.info("Found {} instruments in database for data download", instruments.size());
            log.info("Download configuration: {}", config);

            // Download data for all instruments
            downloadMarketDataForInstruments(instruments, config);

        } catch (Exception e) {
            log.error("Error in data download process", e);
            System.exit(1);
        } finally {
            dbManager.closeConnection();
        }
    }

    /**
     * Download market data for a list of instruments with comprehensive error handling.
     */
    private static void downloadMarketDataForInstruments(List<Instrument> instruments, MarketDataDownloadConfig config) {
        DataProvider provider = new YahooFinanceProvider();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);

        log.info("Starting market data download for {} instruments", instruments.size());

        for (int i = 0; i < instruments.size(); i++) {
            Instrument instrument = instruments.get(i);
            String symbol = instrument.getSymbol();

            log.info("Processing instrument {}/{}: {} ({})",
                       i + 1, instruments.size(), symbol, instrument.getName());

            try {
                boolean success = downloadDataForInstrument(instrument, provider, config);
                if (success) {
                    successCount.incrementAndGet();
                    log.info("✓ Successfully processed {}", symbol);
                } else {
                    skippedCount.incrementAndGet();
                    log.info("⊘ Skipped {} (data already up to date)", symbol);
                }

            } catch (Exception e) {
                errorCount.incrementAndGet();
                log.error("✗ Failed to process instrument {}: {}", symbol, e.getMessage(), e);

                if (!config.isContinueOnError()) {
                    log.error("Stopping download process due to error (continueOnError=false)");
                    break;
                }
            }

            // Add a small delay between downloads to be respectful to the data provider
            if (i < instruments.size() - 1) {
                try {
                    Thread.sleep(500); // 500ms delay
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Download process interrupted");
                    break;
                }
            }
        }

        // Log final summary
        log.info("Market data download completed:");
        log.info("  ✓ Successful: {}", successCount.get());
        log.info("  ⊘ Skipped: {}", skippedCount.get());
        log.info("  ✗ Errors: {}", errorCount.get());
        log.info("  📊 Total processed: {}/{}",
                   successCount.get() + errorCount.get() + skippedCount.get(), instruments.size());
    }

    /**
     * Download data for a single instrument with retry logic.
     * @return true if data was downloaded, false if skipped
     */
    private static boolean downloadDataForInstrument(Instrument instrument, DataProvider provider, MarketDataDownloadConfig config) {
        String symbol = instrument.getSymbol();

        // Determine start date for this instrument
        LocalDate startDate = determineStartDate(instrument, config);
        LocalDate endDate = config.getEndDate();

        // Check if we need to download data
        if (startDate.isAfter(endDate)) {
            log.debug("Start date {} is after end date {} for {}, skipping", startDate, endDate, symbol);
            return false;
        }

        if (config.isSkipExistingData() && startDate.isAfter(LocalDate.now().minusDays(1))) {
            log.debug("Data appears to be up to date for {}, skipping", symbol);
            return false;
        }

        // Attempt download with retries
        int attempts = 0;
        int maxRetries = config.getMaxRetries();

        while (attempts <= maxRetries) {
            try {
                log.info("Downloading data for {} from {} to {} (attempt {}/{})",
                           symbol, startDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                           endDate.format(DateTimeFormatter.ISO_LOCAL_DATE), attempts + 1, maxRetries + 1);

                provider.downloadHistoricalData(instrument, startDate, endDate, dbManager);
                return true;

            } catch (Exception e) {
                attempts++;
                if (attempts <= maxRetries) {
                    log.warn("Attempt {}/{} failed for {}: {}. Retrying in {}ms...",
                               attempts, maxRetries + 1, symbol, e.getMessage(), config.getRetryDelayMs());
                    try {
                        Thread.sleep(config.getRetryDelayMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Download interrupted", ie);
                    }
                } else {
                    throw new RuntimeException("Failed to download data after " + (maxRetries + 1) + " attempts", e);
                }
            }
        }

        return false;
    }

    /**
     * Determine the appropriate start date for downloading data for an instrument.
     */
    private static LocalDate determineStartDate(Instrument instrument, MarketDataDownloadConfig config) {
        String symbol = instrument.getSymbol();

        // If config specifies a start date, use it
        if (config.getStartDate() != null && !config.isSkipExistingData()) {
            return config.getStartDate();
        }

        // Get the last date we have data for
        LocalDate lastDataDate = dbManager.getLastDataDate(symbol);

        if (lastDataDate == null) {
            // No existing data, use config start date or default
            LocalDate configStartDate = config.getStartDate();
            if (configStartDate != null) {
                return configStartDate;
            } else {
                // Default to historical data start
                return LocalDate.now().minusYears(DEFAULT_HISTORY_YEARS);
            }
        } else {
            // We have existing data
            if (config.isSkipExistingData()) {
                // Start from a few days before the last data point to handle potential gaps
                return lastDataDate.minusDays(7);
            } else {
                // Use config start date or the last data date
                LocalDate configStartDate = config.getStartDate();
                return configStartDate != null ? configStartDate : lastDataDate.minusDays(7);
            }
        }
    }

    /**
     * Parse command line arguments to create download configuration.
     */
    static MarketDataDownloadConfig parseCommandLineArgs(String[] args) {
        MarketDataDownloadConfig config = new MarketDataDownloadConfig();

        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--start-date":
                    if (i + 1 < args.length) {
                        config.setStartDate(LocalDate.parse(args[++i]));
                    }
                    break;
                case "--end-date":
                    if (i + 1 < args.length) {
                        config.setEndDate(LocalDate.parse(args[++i]));
                    }
                    break;
                case "--full-history":
                    config = MarketDataDownloadConfig.fullHistoricalData();
                    break;
                case "--recent-only":
                    config = MarketDataDownloadConfig.recentDataUpdate();
                    break;
                case "--no-skip-existing":
                    config.setSkipExistingData(false);
                    break;
                case "--stop-on-error":
                    config.setContinueOnError(false);
                    break;
                case "--max-retries":
                    if (i + 1 < args.length) {
                        config.setMaxRetries(Integer.parseInt(args[++i]));
                    }
                    break;
                case "--help":
                    printUsage();
                    System.exit(0);
                    break;
            }
        }

        return config;
    }

    /**
     * Print usage information.
     */
    private static void printUsage() {
        System.out.println("Market Data Downloader - Automatically downloads OHLCV data for all instruments in database");
        System.out.println();
        System.out.println("Usage: java -jar market-data-downloader.jar [options]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  --start-date YYYY-MM-DD    Start date for historical data (default: calculated based on existing data)");
        System.out.println("  --end-date YYYY-MM-DD      End date for historical data (default: today)");
        System.out.println("  --full-history             Download complete historical data from 1962");
        System.out.println("  --recent-only              Download only recent data (last 30 days)");
        System.out.println("  --no-skip-existing         Re-download all data, don't skip existing");
        System.out.println("  --stop-on-error            Stop processing if any symbol fails (default: continue)");
        System.out.println("  --max-retries N            Maximum retries per symbol (default: 3)");
        System.out.println("  --help                     Show this help message");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar market-data-downloader.jar");
        System.out.println("  java -jar market-data-downloader.jar --recent-only");
        System.out.println("  java -jar market-data-downloader.jar --start-date 2020-01-01 --end-date 2023-12-31");
        System.out.println("  java -jar market-data-downloader.jar --full-history --no-skip-existing");
    }
}
