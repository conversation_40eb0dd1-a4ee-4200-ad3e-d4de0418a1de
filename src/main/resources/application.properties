# Server configuration
server.port=8080
server.servlet.context-path=/investment-toolkit

# Connection timeout configuration for long-running operations
# Set connection timeout to 45 minutes (2700 seconds) to handle DMI calculations
server.tomcat.connection-timeout=2700000
server.tomcat.keep-alive-timeout=2700000

# HTTP request timeout settings
spring.mvc.async.request-timeout=2700000

# Database configuration
spring.datasource.url=************************************
spring.datasource.username=sa
spring.datasource.password=

# Logging configuration
logging.level.root=INFO
logging.level.com.investment=DEBUG
logging.file.name=logs/investment-toolkit.log

# OpenAPI/Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
