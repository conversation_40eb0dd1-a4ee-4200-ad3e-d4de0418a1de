package com.investment.api.controller

import com.investment.api.model.CreateWatchListRequest
import com.investment.api.model.ReorderWatchListRequest
import com.investment.api.model.UpdateWatchListRequest
import com.investment.api.model.CsvUploadResponse
import com.investment.model.WatchListItem
import com.investment.service.WatchListService
import com.investment.service.TechnicalSignalService
import org.springframework.http.HttpStatus
import org.springframework.mock.web.MockMultipartFile
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.SQLException
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for WatchListController.
 */
class WatchListControllerSpec extends Specification {

    WatchListController watchListController
    WatchListService watchListService
    TechnicalSignalService technicalSignalService

    def setup() {
        watchListService = Mock(WatchListService)
        technicalSignalService = Mock(TechnicalSignalService)
        watchListController = new WatchListController(watchListService, technicalSignalService)
    }

    def "should create watch list item successfully"() {
        given: "a valid create watch list request"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.of(2024, 1, 15), "Strong growth potential")

        and: "service creates item successfully"
        def createdItem = new WatchListItem(1, "AAPL", LocalDate.of(2024, 1, 15), "Strong growth potential")
        createdItem.setId(1L)
        
        watchListService.createWatchListItem(request) >> createdItem

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.data.displayIndex == 1
        response.body.data.startDate == LocalDate.of(2024, 1, 15)
        response.body.data.remarks == "Strong growth potential"
        response.body.message == "Watch list item created successfully"
    }

    def "should handle create watch list item validation error"() {
        given: "service throws IllegalArgumentException"
        def request = new CreateWatchListRequest(1, "INVALID", LocalDate.now())
        watchListService.createWatchListItem(request) >> { throw new IllegalArgumentException("Symbol not found") }

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Symbol not found")
    }

    def "should handle create watch list item database error"() {
        given: "service throws SQLException"
        def request = new CreateWatchListRequest(1, "AAPL", LocalDate.now())
        watchListService.createWatchListItem(request) >> { throw new SQLException("Database error") }

        when: "calling create watch list item endpoint"
        def response = watchListController.createWatchListItem(request)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to create watch list item: Database error")
    }

    def "should get all watch list items successfully"() {
        given: "service returns watch list items"
        def item1 = createSampleWatchListItem(1L, "AAPL", 1)
        def item2 = createSampleWatchListItem(2L, "MSFT", 2)
        watchListService.getAllWatchListItems() >> [item1, item2]

        when: "calling get all watch list items endpoint"
        def response = watchListController.getAllWatchListItems()

        then: "should return successful response"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.size() == 2
        response.body.data[0].symbol == "AAPL"
        response.body.data[1].symbol == "MSFT"
        response.body.message == "Retrieved 2 watch list items"
    }

    def "should get watch list item by ID successfully"() {
        given: "service returns watch list item"
        def item = createSampleWatchListItem(1L, "AAPL", 1)
        watchListService.getWatchListItemById(1L) >> Optional.of(item)

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(1L)

        then: "should return watch list item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.symbol == "AAPL"
        response.body.message == "Watch list item found"
    }

    def "should return 404 for non-existent watch list item"() {
        given: "service returns empty optional"
        watchListService.getWatchListItemById(999L) >> Optional.empty()

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Watch list item not found: 999")
    }

    def "should update watch list item successfully"() {
        given: "a valid update request"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        updateRequest.setRemarks("Updated analysis")

        and: "service updates item successfully"
        def updatedItem = createSampleWatchListItem(1L, "AAPL", 5)
        updatedItem.setRemarks("Updated analysis")
        watchListService.updateWatchListItem(1L, updateRequest) >> updatedItem

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return updated item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.id == 1L
        response.body.data.displayIndex == 5
        response.body.data.remarks == "Updated analysis"
        response.body.message == "Watch list item updated successfully"
    }

    def "should handle update with no fields provided"() {
        given: "an empty update request"
        def updateRequest = new UpdateWatchListRequest()

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message == "No update fields provided"
    }

    def "should update performance metrics successfully"() {
        given: "service updates performance successfully"
        def updatedItem = createSampleWatchListItem(1L, "AAPL", 1)
        updatedItem.setOneMonthPerf(new BigDecimal("0.05"))
        updatedItem.setThreeMonthPerf(new BigDecimal("0.12"))
        watchListService.updateWatchListPerformance(1L, new BigDecimal("0.05"), new BigDecimal("0.12"), null) >> updatedItem

        when: "calling update performance endpoint"
        def response = watchListController.updateWatchListPerformance(1L, new BigDecimal("0.05"), new BigDecimal("0.12"), null)

        then: "should return updated item"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data.oneMonthPerf == new BigDecimal("0.05")
        response.body.data.threeMonthPerf == new BigDecimal("0.12")
        response.body.message == "Performance metrics updated successfully"
    }

    def "should delete watch list item successfully"() {
        given: "service deletes item successfully"
        watchListService.deleteWatchListItem(1L) >> true

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(1L)

        then: "should return success"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data == "Watch list item 1 deleted"
        response.body.message == "Watch list item deleted successfully"
    }

    def "should handle delete non-existent watch list item"() {
        given: "service returns false for deletion"
        watchListService.deleteWatchListItem(999L) >> false

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(999L)

        then: "should return 404"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Watch list item not found: 999")
    }

    def "should reorder watch list items successfully"() {
        given: "a valid reorder request"
        def idToIndexMap = [1L: 0, 2L: 1, 3L: 2]
        def request = new ReorderWatchListRequest(idToIndexMap)

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return success"
        response.statusCode.is2xxSuccessful()
        response.body.success
        response.body.data == "Reordered 3 items"
        response.body.message == "Watch list items reordered successfully"

        and: "service should be called"
        1 * watchListService.reorderWatchListItems(idToIndexMap)
    }

    def "should handle invalid reorder request"() {
        given: "an invalid reorder request with duplicate indexes"
        def idToIndexMap = [1L: 0, 2L: 0] // Duplicate index
        def request = new ReorderWatchListRequest(idToIndexMap)

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Duplicate display indexes are not allowed")
    }

    def "should handle reorder with non-existent item"() {
        given: "a reorder request"
        def idToIndexMap = [999L: 0]
        def request = new ReorderWatchListRequest(idToIndexMap)
        watchListService.reorderWatchListItems(idToIndexMap) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling reorder watch list items endpoint"
        def response = watchListController.reorderWatchListItems(request)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle database errors gracefully"() {
        given: "service throws SQLException"
        watchListService.getAllWatchListItems() >> { throw new SQLException("Database connection failed") }

        when: "calling get all watch list items endpoint"
        def response = watchListController.getAllWatchListItems()

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to retrieve watch list items: Database connection failed")
    }

    def "should handle update watch list item not found"() {
        given: "service throws IllegalArgumentException for non-existent item"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        watchListService.updateWatchListItem(999L, updateRequest) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(999L, updateRequest)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle performance update for non-existent item"() {
        given: "service throws IllegalArgumentException for non-existent item"
        watchListService.updateWatchListPerformance(999L, _, _, _) >> { throw new IllegalArgumentException("Watch list item not found: 999") }

        when: "calling update performance endpoint"
        def response = watchListController.updateWatchListPerformance(999L, new BigDecimal("0.05"), null, null)

        then: "should return bad request"
        response.statusCode.is4xxClientError()
        !response.body.success
        response.body.message.contains("Invalid request: Watch list item not found: 999")
    }

    def "should handle database error in get watch list item by ID"() {
        given: "service throws SQLException"
        watchListService.getWatchListItemById(1L) >> { throw new SQLException("Database error") }

        when: "calling get watch list item endpoint"
        def response = watchListController.getWatchListItem(1L)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to retrieve watch list item: Database error")
    }

    def "should handle database error in update watch list item"() {
        given: "service throws SQLException"
        def updateRequest = new UpdateWatchListRequest()
        updateRequest.setDisplayIndex(5)
        watchListService.updateWatchListItem(1L, updateRequest) >> { throw new SQLException("Database error") }

        when: "calling update watch list item endpoint"
        def response = watchListController.updateWatchListItem(1L, updateRequest)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to update watch list item: Database error")
    }

    def "should handle database error in delete watch list item"() {
        given: "service throws SQLException"
        watchListService.deleteWatchListItem(1L) >> { throw new SQLException("Database error") }

        when: "calling delete watch list item endpoint"
        def response = watchListController.deleteWatchListItem(1L)

        then: "should return internal server error"
        response.statusCode.is5xxServerError()
        !response.body.success
        response.body.message.contains("Failed to delete watch list item: Database error")
    }

    def "should upload TradingView CSV file successfully in dry run mode"() {
        given: "a valid TradingView CSV file"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
AAPL,Apple Inc.,150.00,USD,1.01%,50000000,1.2,2.5T,USD,Technology,Buy,5.23%,12.45%,18.67%
MSFT,Microsoft Corporation,300.00,USD,0.67%,30000000,0.9,2.2T,USD,Technology,Buy,3.45%,8.90%,15.23%"""
        def file = new MockMultipartFile("file", "tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(2, 2, 0, 2, 0, 0, 0, [], ["AAPL", "MSFT"], true)
        watchListService.processTradingViewCsvFile(file, true, 1000, true, true) >> mockResponse

        when: "uploading TradingView CSV file in dry run mode"
        def response = watchListController.uploadUsTradingViewCsv(file, true, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("TradingView CSV validation completed")
    }

    def "should upload TradingView CSV file and save to watch list when dry run is false"() {
        given: "a valid TradingView CSV file"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
AAPL,Apple Inc.,150.00,USD,1.01%,50000000,1.2,2.5T,USD,Technology,Buy,5.23%,12.45%,18.67%"""
        def file = new MockMultipartFile("file", "tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 1, 0, [], ["AAPL"], false)
        watchListService.processTradingViewCsvFile(file, false, 1000, true, true) >> mockResponse

        when: "uploading TradingView CSV file with dry run false"
        def response = watchListController.uploadUsTradingViewCsv(file, false, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("TradingView CSV import completed")
        response.body.data.addedInstruments == 1
        response.body.data.dryRun == false
    }

    def "should handle empty TradingView CSV file upload"() {
        given: "an empty file"
        def file = new MockMultipartFile("file", "empty.csv", "text/csv", "".bytes)

        when: "uploading empty file"
        def response = watchListController.uploadUsTradingViewCsv(file, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle null TradingView CSV file upload"() {
        when: "uploading null file"
        def response = watchListController.uploadUsTradingViewCsv(null, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle TradingView CSV processing errors"() {
        given: "a CSV file that causes processing error"
        def file = new MockMultipartFile("file", "invalid.csv", "text/csv", "invalid content".bytes)
        watchListService.processTradingViewCsvFile(file, true, 1000, true, true) >> { throw new RuntimeException("Processing error") }

        when: "uploading TradingView CSV file"
        def response = watchListController.uploadUsTradingViewCsv(file, true, 1000, true, true)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("TradingView CSV processing failed")
        response.body.message.contains("Processing error")
    }

    def "should use custom parameters for TradingView CSV upload"() {
        given: "a TradingView CSV file with custom parameters"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
AAPL,Apple Inc.,150.00,USD,1.01%,50000000,1.2,2.5T,USD,Technology,Buy,5.23%,12.45%,18.67%"""
        def file = new MockMultipartFile("file", "tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 1, 0, [], ["AAPL"], false)

        when: "uploading with custom parameters"
        def response = watchListController.uploadUsTradingViewCsv(file, false, 500, false, false)

        then: "should pass custom parameters to service"
        1 * watchListService.processTradingViewCsvFile(file, false, 500, false, false) >> mockResponse
        response.statusCode == HttpStatus.OK
        response.body.success == true
    }

    def "should upload Hong Kong TradingView CSV file successfully in dry run mode"() {
        given: "a valid Hong Kong TradingView CSV file"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
2282,MGM CHINA HLDGS LTD,15.86,HKD,0.37974683544302984,9856746,0.7767440326916403,60040000725,HKD,Consumer services,Strong buy,42.88%,72.39%,49.62%
9926,AKESO INC,139.5,HKD,10.714285714285714,16822566,1.1905331552788203,113096743286,HKD,Health technology,Strong buy,39.5%,66.47%,143.03%"""
        def file = new MockMultipartFile("file", "hk_tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(2, 2, 0, 2, 0, 0, 0, [], ["2282.HK", "9926.HK"], true)
        watchListService.processHkTradingViewCsvFile(file, true, 1000, true, true) >> mockResponse

        when: "uploading Hong Kong TradingView CSV file in dry run mode"
        def response = watchListController.uploadHkTradingViewCsv(file, true, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("Hong Kong TradingView CSV validation completed")
    }

    def "should upload Hong Kong TradingView CSV file and save to watch list when dry run is false"() {
        given: "a valid Hong Kong TradingView CSV file"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
2282,MGM CHINA HLDGS LTD,15.86,HKD,0.37974683544302984,9856746,0.7767440326916403,60040000725,HKD,Consumer services,Strong buy,42.88%,72.39%,49.62%"""
        def file = new MockMultipartFile("file", "hk_tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 1, 0, [], ["2282.HK"], false)
        watchListService.processHkTradingViewCsvFile(file, false, 1000, true, true) >> mockResponse

        when: "uploading Hong Kong TradingView CSV file with dry run false"
        def response = watchListController.uploadHkTradingViewCsv(file, false, 1000, true, true)

        then: "should return successful response"
        response.statusCode == HttpStatus.OK
        response.body.success == true
        response.body.data == mockResponse
        response.body.message.contains("Hong Kong TradingView CSV import completed")
        response.body.data.addedInstruments == 1
        response.body.data.dryRun == false
    }

    def "should handle empty Hong Kong TradingView CSV file upload"() {
        given: "an empty file"
        def file = new MockMultipartFile("file", "empty.csv", "text/csv", "".bytes)

        when: "uploading empty file"
        def response = watchListController.uploadHkTradingViewCsv(file, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle null Hong Kong TradingView CSV file upload"() {
        when: "uploading null file"
        def response = watchListController.uploadHkTradingViewCsv(null, true, 1000, true, true)

        then: "should return bad request"
        response.statusCode == HttpStatus.BAD_REQUEST
        response.body.success == false
        response.body.message.contains("File is required and cannot be empty")
    }

    def "should handle Hong Kong TradingView CSV processing errors"() {
        given: "a CSV file that causes processing error"
        def file = new MockMultipartFile("file", "invalid.csv", "text/csv", "invalid content".bytes)
        watchListService.processHkTradingViewCsvFile(file, true, 1000, true, true) >> { throw new RuntimeException("Processing error") }

        when: "uploading Hong Kong TradingView CSV file"
        def response = watchListController.uploadHkTradingViewCsv(file, true, 1000, true, true)

        then: "should return error response"
        response.statusCode == HttpStatus.INTERNAL_SERVER_ERROR
        response.body.success == false
        response.body.message.contains("Hong Kong TradingView CSV processing failed")
        response.body.message.contains("Processing error")
    }

    def "should use custom parameters for Hong Kong TradingView CSV upload"() {
        given: "a Hong Kong TradingView CSV file with custom parameters"
        def csvContent = """Symbol,Description,Price,Price - Currency,Price Change % 1 day,Volume 1 day,Relative Volume 1 day,Market capitalization,Market capitalization - Currency,Sector,Analyst Rating,Performance % 1 month,Performance % 3 months,Performance % 6 months
2282,MGM CHINA HLDGS LTD,15.86,HKD,0.37974683544302984,9856746,0.7767440326916403,60040000725,HKD,Consumer services,Strong buy,42.88%,72.39%,49.62%"""
        def file = new MockMultipartFile("file", "hk_tradingview.csv", "text/csv", csvContent.bytes)
        def mockResponse = new CsvUploadResponse(1, 1, 0, 1, 0, 1, 0, [], ["2282.HK"], false)

        when: "uploading with custom parameters"
        def response = watchListController.uploadHkTradingViewCsv(file, false, 500, false, false)

        then: "should pass custom parameters to service"
        1 * watchListService.processHkTradingViewCsvFile(file, false, 500, false, false) >> mockResponse
        response.statusCode == HttpStatus.OK
        response.body.success == true
    }

    private WatchListItem createSampleWatchListItem(Long id, String symbol, Integer displayIndex) {
        def item = new WatchListItem(displayIndex, symbol, LocalDate.of(2024, 1, 15))
        item.setId(id)
        item.setCreatedDate(LocalDateTime.now())
        item.setUpdatedDate(LocalDateTime.now())
        return item
    }
}
