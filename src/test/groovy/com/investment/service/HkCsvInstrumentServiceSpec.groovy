package com.investment.service

import com.investment.database.DatabaseManager
import com.investment.model.InstrumentType
import org.springframework.mock.web.MockMultipartFile
import spock.lang.Specification

/**
 * Test specification for Hong Kong CSV processing functionality in CsvInstrumentService.
 */
class HkCsvInstrumentServiceSpec extends Specification {

    CsvInstrumentService csvInstrumentService
    DatabaseManager databaseManager

    def setup() {
        databaseManager = Mock(DatabaseManager)
        csvInstrumentService = new CsvInstrumentService(databaseManager)
    }

    def "should process Hong Kong CSV with English headers successfully"() {
        given: "a CSV file with English headers and Hong Kong stock data"
        def csvContent = """Symbol,Name
00001,長和
00002,中電控股
00176,先機企業集團
01044,恒安國際
89618,京東集團 - S W R"""

        def file = new MockMultipartFile("file", "hk_stocks.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing the Hong Kong CSV file in dry run mode"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should process all rows successfully"
        response.totalRowsInCsv == 5
        response.validRows == 5
        response.invalidRows == 0
        response.processedInstruments == 5
        response.skippedInstruments == 0
        response.addedInstruments == 0
        response.updatedInstruments == 0
        response.validationErrors.isEmpty()
        response.processedSymbols.containsAll([
            "0001.HK", "0002.HK", "0176.HK", "1044.HK", "89618.HK"
        ])
        response.dryRun == true
    }

    def "should process Hong Kong CSV with Chinese headers successfully"() {
        given: "a CSV file with Traditional Chinese headers"
        def csvContent = """股份代號,股份名稱
00001,長和
00002,中電控股"""

        def file = new MockMultipartFile("file", "hk_stocks_chinese.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing the Hong Kong CSV file"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should process all rows successfully"
        response.totalRowsInCsv == 2
        response.validRows == 2
        response.processedInstruments == 2
        response.processedSymbols.containsAll(["0001.HK", "0002.HK"])
    }

    def "should transform Hong Kong symbols correctly"() {
        given: "a CSV file with various symbol formats"
        def csvContent = """Symbol,Name
1,測試公司1
00001,測試公司2
0123,測試公司3
12345,測試公司4"""

        def file = new MockMultipartFile("file", "hk_symbols.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing the Hong Kong CSV file"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should transform symbols correctly"
        response.processedSymbols.containsAll([
            "0001.HK",   // 1 → 0001.HK
            "0001.HK",   // 00001 → 0001.HK (duplicate will be handled)
            "0123.HK",   // 0123 → 0123.HK
            "12345.HK"   // 12345 → 12345.HK (no padding needed)
        ])
    }

    def "should skip duplicate symbols when skipDuplicates is true"() {
        given: "existing symbols in database and CSV with duplicates"
        def csvContent = """Symbol,Name
00001,長和
00002,中電控股
00001,重複符號"""

        def file = new MockMultipartFile("file", "hk_duplicates.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> ["0001.HK"] // 0001.HK already exists

        when: "processing with skipDuplicates enabled"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should skip existing and duplicate symbols"
        response.totalRowsInCsv == 3
        response.validRows == 3
        response.processedInstruments == 1  // Only 0002.HK processed
        response.skippedInstruments == 2    // 0001.HK (exists) and duplicate 0001.HK
        response.processedSymbols == ["0002.HK"]
    }

    def "should save Hong Kong instruments to database when not in dry run mode"() {
        given: "a CSV file with Hong Kong stock data"
        def csvContent = """Symbol,Name
00001,長和
00002,中電控股"""

        def file = new MockMultipartFile("file", "hk_stocks.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing in actual save mode (not dry run)"
        def response = csvInstrumentService.processHkCsvFile(file, false, 1000, true, true)

        then: "should save instruments to database with correct parameters"
        2 * databaseManager.saveInstrumentWithDetails(_, _, _, _, _, _, _, _)

        and: "response should indicate successful processing"
        response.processedInstruments == 2
        response.addedInstruments == 2
        response.updatedInstruments == 0
        response.dryRun == false
    }

    def "should handle validation errors for missing required fields"() {
        given: "a CSV file with missing data"
        def csvContent = """Symbol,Name
,長和
00002,"""

        def file = new MockMultipartFile("file", "hk_invalid.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing with validation enabled"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should report validation errors"
        response.totalRowsInCsv == 2
        response.validRows == 0
        response.invalidRows == 2
        response.processedInstruments == 0
        response.validationErrors.size() == 2
        response.validationErrors.any { it.contains("Symbol (column 1) is required") }
        response.validationErrors.any { it.contains("Name (column 2) is required") }
    }

    def "should handle insufficient columns in CSV"() {
        given: "a CSV file with only one column"
        def csvContent = """Symbol
00001
00002"""

        def file = new MockMultipartFile("file", "hk_insufficient.csv", "text/csv", csvContent.bytes)

        when: "processing the invalid CSV file"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should return validation error"
        response.totalRowsInCsv == 0
        response.validationErrors.size() == 1
        response.validationErrors[0].contains("Hong Kong CSV must have at least 2 columns")
    }

    def "should respect maxInstruments limit"() {
        given: "a CSV file with multiple instruments"
        def csvContent = """Symbol,Name
00001,公司1
00002,公司2
00003,公司3"""

        def file = new MockMultipartFile("file", "hk_limit.csv", "text/csv", csvContent.bytes)
        databaseManager.getAllSymbols() >> []

        when: "processing with maxInstruments limit of 2"
        def response = csvInstrumentService.processHkCsvFile(file, true, 2, true, true)

        then: "should process only 2 instruments"
        response.totalRowsInCsv == 3  // All rows are counted, but processing stops at limit
        response.processedInstruments == 2
        response.processedSymbols.size() == 2
    }

    def "should handle empty file gracefully"() {
        given: "an empty CSV file"
        def file = new MockMultipartFile("file", "empty.csv", "text/csv", "".bytes)

        when: "processing the empty file"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should return appropriate error"
        response.totalRowsInCsv == 0
        response.validationErrors.size() == 1
        response.validationErrors[0] == "Uploaded file is empty"
    }

    def "should handle non-CSV file gracefully"() {
        given: "a non-CSV file"
        def file = new MockMultipartFile("file", "data.txt", "text/plain", "some text".bytes)

        when: "processing the non-CSV file"
        def response = csvInstrumentService.processHkCsvFile(file, true, 1000, true, true)

        then: "should return appropriate error"
        response.validationErrors.size() == 1
        response.validationErrors[0] == "Invalid file format. Please upload a CSV file."
    }
}
