plugins {
    id 'java'
    id 'groovy'
    id 'application'
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
}

group 'com.investment'
version '1.0-SNAPSHOT'

sourceCompatibility = 21
targetCompatibility = 21

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // Explicitly use Log4j2 instead of default Logback
    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    
    // Exclude Spring Boot's default logging
    configurations.all {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    
    // high performance low latency library
    implementation("org.agrona:agrona:2.2.0")
    implementation("it.unimi.dsi:fastutil:8.5.15")
    implementation("org.eclipse.collections:eclipse-collections:11.1.0")
    implementation("org.eclipse.collections:eclipse-collections-api:11.1.0")

    // OpenAPI/Swagger documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'

    // Database
    implementation 'org.duckdb:duckdb_jdbc:*******'

    // HTTP client
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'

    // JSON parsing
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2'

    // CSV parsing
    implementation 'org.apache.commons:commons-csv:1.10.0'

    // HTML parsing
    implementation 'org.jsoup:jsoup:1.17.2'

    // Logging is provided by Spring Boot starter

    // Lombok for reducing boilerplate code
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    testCompileOnly 'org.projectlombok:lombok:1.18.30'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.30'

    // Testing
    // Keep JUnit for backward compatibility
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.3'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.3'

    // Groovy and Spock
    implementation 'org.codehaus.groovy:groovy:3.0.19'
    testImplementation 'org.codehaus.groovy:groovy-all:3.0.19'
    testImplementation 'org.spockframework:spock-core:2.3-groovy-3.0'
    testImplementation 'org.spockframework:spock-junit4:2.3-groovy-3.0'

    // Mockito for Java mocking
//    testImplementation 'org.mockito:mockito-core:5.3.1'
    testImplementation 'net.bytebuddy:byte-buddy:1.14.16'
}

application {
    mainClass = 'com.investment.InvestmentApplication'
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

// Configure Groovy source sets
sourceSets {
    test {
        groovy {
            srcDirs = ['src/test/groovy']
        }
    }
}
