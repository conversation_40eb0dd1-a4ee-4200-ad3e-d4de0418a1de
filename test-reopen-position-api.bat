@echo off
echo Testing Position Reopen API Functionality
echo ==========================================

echo.
echo 1. Creating a test position...
curl -X POST "http://localhost:8080/investment-toolkit/api/positions" ^
  -H "Content-Type: application/json" ^
  -d "{\"symbol\":\"AAPL\",\"position\":100,\"side\":\"BUY\",\"tradePrice\":150.00}"

echo.
echo.
echo 2. Getting all positions to see the created position...
curl -X GET "http://localhost:8080/investment-toolkit/api/positions"

echo.
echo.
echo 3. Closing the position (using ID 35)...
curl -X POST "http://localhost:8080/investment-toolkit/api/positions/35/close"

echo.
echo.
echo 4. Verifying position is closed...
curl -X GET "http://localhost:8080/investment-toolkit/api/positions/35"

echo.
echo.
echo 5. Reopening the position...
curl -X POST "http://localhost:8080/investment-toolkit/api/positions/35/reopen"

echo.
echo.
echo 6. Verifying position is reopened...
curl -X GET "http://localhost:8080/investment-toolkit/api/positions/35"

echo.
echo.
echo 7. Testing error case - trying to reopen an already open position...
curl -X POST "http://localhost:8080/investment-toolkit/api/positions/35/reopen"

echo.
echo.
echo Test completed!
pause
