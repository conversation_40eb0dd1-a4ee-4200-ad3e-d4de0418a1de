import { get, post, put, del, postLongRunning, postUltraLongRunning } from './apiClient';
import {
  WatchListItem,
  CreateWatchListRequest,
  UpdateWatchListRequest,
  ReorderWatchListRequest,
  RecalculatePerformanceResponse,
  WatchListUpdateRequest,
  WatchListUpdateResult,
  CsvUploadResponse,
  BulkUpdateProgress,
  BulkUpdateResult,
  ApiResponse
} from '../../types/api';
import { OHLCVService } from './ohlcvService';
import { TechnicalIndicatorService } from './technicalIndicatorService';

export class WatchListService {

  /**
   * Get all watch list items
   */
  static async getWatchListItems(): Promise<ApiResponse<WatchListItem[]>> {
    return get<WatchListItem[]>('/watchlist');
  }

  /**
   * Get watch list item by ID
   */
  static async getWatchListItemById(id: number): Promise<ApiResponse<WatchListItem>> {
    return get<WatchListItem>(`/watchlist/${id}`);
  }

  /**
   * Create new watch list item
   */
  static async createWatchListItem(request: CreateWatchListRequest): Promise<ApiResponse<WatchListItem>> {
    return post<WatchListItem>('/watchlist', request);
  }

  /**
   * Update existing watch list item
   */
  static async updateWatchListItem(id: number, request: UpdateWatchListRequest): Promise<ApiResponse<WatchListItem>> {
    return put<WatchListItem>(`/watchlist/${id}`, request);
  }

  /**
   * Delete watch list item
   */
  static async deleteWatchListItem(id: number): Promise<ApiResponse<string>> {
    return del<string>(`/watchlist/${id}`);
  }

  /**
   * Update performance metrics for a watch list item
   */
  static async updatePerformance(
    id: number,
    oneMonthPerf?: number,
    threeMonthPerf?: number,
    sixMonthPerf?: number
  ): Promise<ApiResponse<WatchListItem>> {
    return post<WatchListItem>(`/watchlist/${id}/performance`, {
      oneMonthPerformance: oneMonthPerf,
      threeMonthPerformance: threeMonthPerf,
      sixMonthPerformance: sixMonthPerf
    });
  }

  /**
   * Reorder watch list items
   */
  static async reorderWatchListItems(request: ReorderWatchListRequest): Promise<ApiResponse<string>> {
    return put<string>('/watchlist/reorder', request);
  }

  /**
   * Recalculate performance metrics for all watch list items
   */
  static async recalculatePerformance(): Promise<ApiResponse<RecalculatePerformanceResponse>> {
    return post<RecalculatePerformanceResponse>('/watchlist/recalculate-performance', {});
  }

  /**
   * Update OHLCV data for all watch list symbols and recalculate technical indicators
   * Uses ultra-long timeout (40 minutes) to handle DMI calculations that can take 30+ minutes
   */
  static async updateOHLCVDataForWatchList(
    request: WatchListUpdateRequest = {}
  ): Promise<ApiResponse<WatchListUpdateResult>> {
    // Use ultra-long-running endpoint for DMI calculations that can take 30+ minutes
    return postUltraLongRunning<WatchListUpdateResult>('/watchlist/update-ohlcv-data', request);
  }

  /**
   * Recalculate technical signal streaks for all watch list items
   */
  static async recalculateTechnicalSignals(): Promise<ApiResponse<RecalculatePerformanceResponse>> {
    return post<RecalculatePerformanceResponse>('/watchlist/recalculate-technical-signals', {});
  }

  /**
   * Recalculate Bollinger Bands for all watch list symbols using INCREMENTAL mode
   */
  static async recalculateBollingerBandsForWatchList(
    request: WatchListUpdateRequest = {}
  ): Promise<ApiResponse<WatchListUpdateResult>> {
    return post<WatchListUpdateResult>('/watchlist/recalculate-bollinger-bands', request);
  }

  /**
   * Recalculate DMI indicators for all watch list symbols using INCREMENTAL mode
   * Uses long timeout as DMI calculations can take several minutes
   */
  static async recalculateDMIForWatchList(
    request: WatchListUpdateRequest = {}
  ): Promise<ApiResponse<WatchListUpdateResult>> {
    return postLongRunning<WatchListUpdateResult>('/watchlist/recalculate-dmi', request);
  }

  /**
   * Update OHLCV data for all watch list symbols with real-time progress updates
   * Similar to the Financial Instruments page bulk update functionality
   */
  static async bulkUpdateOHLCVDataForWatchList(
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    // First get all watch list items to know which symbols to update
    const watchListResponse = await this.getWatchListItems();
    if (!watchListResponse.success || !watchListResponse.data) {
      throw new Error('Failed to fetch watch list items');
    }

    const symbols = watchListResponse.data.map(item => item.symbol);
    const results: BulkUpdateProgress[] = symbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each symbol individually with progress updates
    for (let i = 0; i < symbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = symbols[i];
      results[i].status = 'processing';

      // Call progress callback to show current symbol being processed
      if (onProgress) {
        onProgress([...results]);
      }

      try {
        // Use the existing OHLCV service to update individual symbol
        const response = await OHLCVService.updateOHLCVData(symbol, undefined, undefined, request.dryRun || false);

        if (response.success) {
          results[i].status = 'success';
          results[i].message = response.message || 'Updated successfully';
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Update failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      // Call progress callback after each symbol
      if (onProgress) {
        onProgress([...results]);
      }

      // Small delay to prevent overwhelming the server
      if (i < symbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: symbols.length,
      successCount,
      errorCount,
      results,
      summary: `Watch list OHLCV update completed: ${successCount} successful, ${errorCount} failed out of ${symbols.length} symbols`
    };
  }

  /**
   * Retry failed symbols from a previous bulk operation
   */
  static async retryFailedSymbolsForWatchList(
    previousResults: BulkUpdateProgress[],
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    const failedSymbols = previousResults
      .filter(result => result.status === 'error')
      .map(result => result.symbol);

    if (failedSymbols.length === 0) {
      return {
        totalSymbols: 0,
        successCount: 0,
        errorCount: 0,
        results: [],
        summary: 'No failed symbols to retry'
      };
    }

    // Create a new request with only the failed symbols
    // We'll simulate this by processing only the failed symbols
    const results: BulkUpdateProgress[] = failedSymbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each failed symbol individually with progress updates
    for (let i = 0; i < failedSymbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = failedSymbols[i];
      results[i].status = 'processing';

      // Call progress callback to show current symbol being processed
      if (onProgress) {
        onProgress([...results]);
      }

      try {
        // Use the existing OHLCV service to update individual symbol
        const response = await OHLCVService.updateOHLCVData(symbol, undefined, undefined, request.dryRun || false);

        if (response.success) {
          results[i].status = 'success';
          results[i].message = response.message || 'Updated successfully';
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Update failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      // Call progress callback after each symbol
      if (onProgress) {
        onProgress([...results]);
      }

      // Small delay to prevent overwhelming the server
      if (i < failedSymbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: failedSymbols.length,
      successCount,
      errorCount,
      results,
      summary: `Retry completed: ${successCount} successful, ${errorCount} failed out of ${failedSymbols.length} symbols`
    };
  }

  /**
   * Recalculate Bollinger Bands for all watch list symbols with real-time progress updates
   */
  static async bulkRecalculateBollingerBandsForWatchList(
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    // First get all watch list items to know which symbols to process
    const watchListResponse = await this.getWatchListItems();
    if (!watchListResponse.success || !watchListResponse.data) {
      throw new Error('Failed to fetch watch list items');
    }

    const symbols = watchListResponse.data.map(item => item.symbol);
    const results: BulkUpdateProgress[] = symbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each symbol individually with progress updates
    for (let i = 0; i < symbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = symbols[i];
      results[i].status = 'processing';

      // Call progress callback to show current symbol being processed
      if (onProgress) {
        onProgress([...results]);
      }

      try {
        // Use the existing Bollinger Bands service to calculate for individual symbol
        const response = await TechnicalIndicatorService.calculateBollingerBands({
          symbols: [symbol],
          calculationMode: 'INCREMENTAL',
          dryRun: request.dryRun || false
        });

        if (response.success && response.data) {
          results[i].status = 'success';
          results[i].message = `Processed ${response.data.processedSymbols || 1} symbols, ${response.data.totalRecordsUpdated || 0} records updated`;
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Calculation failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      // Call progress callback after each symbol
      if (onProgress) {
        onProgress([...results]);
      }

      // Small delay to prevent overwhelming the server
      if (i < symbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: symbols.length,
      successCount,
      errorCount,
      results,
      summary: `Bollinger Bands recalculation completed: ${successCount} successful, ${errorCount} failed out of ${symbols.length} symbols`
    };
  }

  /**
   * Recalculate DMI indicators for all watch list symbols with real-time progress updates
   */
  static async bulkRecalculateDMIForWatchList(
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    // First get all watch list items to know which symbols to process
    const watchListResponse = await this.getWatchListItems();
    if (!watchListResponse.success || !watchListResponse.data) {
      throw new Error('Failed to fetch watch list items');
    }

    const symbols = watchListResponse.data.map(item => item.symbol);
    const results: BulkUpdateProgress[] = symbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each symbol individually with progress updates
    for (let i = 0; i < symbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = symbols[i];
      results[i].status = 'processing';

      // Call progress callback to show current symbol being processed
      if (onProgress) {
        onProgress([...results]);
      }

      try {
        // Use the existing DMI service to calculate for individual symbol
        const response = await TechnicalIndicatorService.calculateDMI({
          symbols: [symbol],
          calculationMode: 'INCREMENTAL',
          calculationMethod: 'HYBRID_SQL_JAVA', // Use hybrid method as per memory
          dryRun: request.dryRun || false
        });

        if (response.success && response.data) {
          results[i].status = 'success';
          results[i].message = `Processed ${response.data.processedSymbols || 1} symbols, ${response.data.totalRecordsUpdated || 0} records updated`;
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Calculation failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      // Call progress callback after each symbol
      if (onProgress) {
        onProgress([...results]);
      }

      // Small delay to prevent overwhelming the server
      if (i < symbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: symbols.length,
      successCount,
      errorCount,
      results,
      summary: `DMI recalculation completed: ${successCount} successful, ${errorCount} failed out of ${symbols.length} symbols`
    };
  }

  /**
   * Retry failed symbols for Bollinger Bands recalculation
   */
  static async retryFailedBollingerBandsForWatchList(
    previousResults: BulkUpdateProgress[],
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    const failedSymbols = previousResults
      .filter(result => result.status === 'error')
      .map(result => result.symbol);

    if (failedSymbols.length === 0) {
      return {
        totalSymbols: 0,
        successCount: 0,
        errorCount: 0,
        results: [],
        summary: 'No failed symbols to retry'
      };
    }

    const results: BulkUpdateProgress[] = failedSymbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each failed symbol individually with progress updates
    for (let i = 0; i < failedSymbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = failedSymbols[i];
      results[i].status = 'processing';

      if (onProgress) {
        onProgress([...results]);
      }

      try {
        const response = await TechnicalIndicatorService.calculateBollingerBands({
          symbols: [symbol],
          calculationMode: 'INCREMENTAL',
          dryRun: request.dryRun || false
        });

        if (response.success && response.data) {
          results[i].status = 'success';
          results[i].message = `Processed ${response.data.processedSymbols || 1} symbols, ${response.data.totalRecordsUpdated || 0} records updated`;
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Calculation failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      if (onProgress) {
        onProgress([...results]);
      }

      if (i < failedSymbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: failedSymbols.length,
      successCount,
      errorCount,
      results,
      summary: `Bollinger Bands retry completed: ${successCount} successful, ${errorCount} failed out of ${failedSymbols.length} symbols`
    };
  }

  /**
   * Retry failed symbols for DMI recalculation
   */
  static async retryFailedDMIForWatchList(
    previousResults: BulkUpdateProgress[],
    request: WatchListUpdateRequest = {},
    onProgress?: (progress: BulkUpdateProgress[]) => void,
    abortSignal?: AbortSignal
  ): Promise<BulkUpdateResult> {
    const failedSymbols = previousResults
      .filter(result => result.status === 'error')
      .map(result => result.symbol);

    if (failedSymbols.length === 0) {
      return {
        totalSymbols: 0,
        successCount: 0,
        errorCount: 0,
        results: [],
        summary: 'No failed symbols to retry'
      };
    }

    const results: BulkUpdateProgress[] = failedSymbols.map(symbol => ({
      symbol,
      status: 'pending' as const,
    }));

    let successCount = 0;
    let errorCount = 0;

    // Process each failed symbol individually with progress updates
    for (let i = 0; i < failedSymbols.length; i++) {
      if (abortSignal?.aborted) {
        throw new Error('Operation was cancelled');
      }

      const symbol = failedSymbols[i];
      results[i].status = 'processing';

      if (onProgress) {
        onProgress([...results]);
      }

      try {
        const response = await TechnicalIndicatorService.calculateDMI({
          symbols: [symbol],
          calculationMode: 'INCREMENTAL',
          calculationMethod: 'HYBRID_SQL_JAVA',
          dryRun: request.dryRun || false
        });

        if (response.success && response.data) {
          results[i].status = 'success';
          results[i].message = `Processed ${response.data.processedSymbols || 1} symbols, ${response.data.totalRecordsUpdated || 0} records updated`;
          successCount++;
        } else {
          results[i].status = 'error';
          results[i].error = response.message || 'Calculation failed';
          errorCount++;
        }
      } catch (error: any) {
        results[i].status = 'error';
        results[i].error = error.message || 'Network error';
        errorCount++;
      }

      if (onProgress) {
        onProgress([...results]);
      }

      if (i < failedSymbols.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      totalSymbols: failedSymbols.length,
      successCount,
      errorCount,
      results,
      summary: `DMI retry completed: ${successCount} successful, ${errorCount} failed out of ${failedSymbols.length} symbols`
    };
  }

  /**
   * Upload TradingView StockScreener CSV file for watch list
   */
  static async uploadUsTradingViewCsv(
    file: File,
    dryRun: boolean = true,
    maxInstruments: number = 10000,
    skipDuplicates: boolean = true,
    validateData: boolean = true
  ): Promise<ApiResponse<CsvUploadResponse>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('dryRun', dryRun.toString());
    formData.append('maxInstruments', maxInstruments.toString());
    formData.append('skipDuplicates', skipDuplicates.toString());
    formData.append('validateData', validateData.toString());

    return postLongRunning<CsvUploadResponse>('/watchlist/upload-us-tradingview-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Upload Hong Kong TradingView StockScreener CSV file for watch list
   */
  static async uploadHkTradingViewCsv(
    file: File,
    dryRun: boolean = true,
    maxInstruments: number = 10000,
    skipDuplicates: boolean = true,
    validateData: boolean = true
  ): Promise<ApiResponse<CsvUploadResponse>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('dryRun', dryRun.toString());
    formData.append('maxInstruments', maxInstruments.toString());
    formData.append('skipDuplicates', skipDuplicates.toString());
    formData.append('validateData', validateData.toString());

    return postLongRunning<CsvUploadResponse>('/watchlist/upload-hk-tradingview-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}
