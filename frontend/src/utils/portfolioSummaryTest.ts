/**
 * Test utility to verify portfolio summary calculations
 * This file demonstrates the currency conversion logic and calculations
 */

import { Position } from '../types/api';

// Currency conversion utility (same as in Positions.tsx)
const convertToUSD = (value: number, symbol: string): number => {
  // Convert HKD to USD for Hong Kong stocks (symbols ending with '.HK')
  if (symbol.toUpperCase().endsWith('.HK')) {
    return value / 7.8; // HKD to USD exchange rate
  }
  return value; // Assume all other symbols are already in USD
};

// Portfolio summary calculations (same as in Positions.tsx)
const calculatePortfolioSummary = (positions: Position[]) => {
  const openPositions = positions.filter(position => position.status === 'OPEN');
  
  if (openPositions.length === 0) {
    return {
      totalInvestmentCapitalUSD: 0,
      totalPnLUSD: 0,
      totalPnLPercent: 0,
      openPositionsCount: 0
    };
  }

  let totalInvestmentCapitalUSD = 0;
  let totalPnLUSD = 0;

  openPositions.forEach(position => {
    // Calculate investment capital (Quantity × Entry Price)
    const quantity = position.position || 0;
    const entryPrice = position.tradePrice || 0;
    
    if (quantity > 0 && entryPrice > 0) {
      const investmentCapital = quantity * entryPrice;
      const investmentCapitalUSD = convertToUSD(investmentCapital, position.symbol);
      totalInvestmentCapitalUSD += investmentCapitalUSD;
    }

    // Add P&L value (already calculated by backend)
    if (position.pnlValue !== undefined && position.pnlValue !== null && !isNaN(position.pnlValue)) {
      const pnlUSD = convertToUSD(position.pnlValue, position.symbol);
      totalPnLUSD += pnlUSD;
    }
  });

  // Calculate total P&L percentage with safety checks
  const totalPnLPercent = totalInvestmentCapitalUSD > 0 
    ? (totalPnLUSD / totalInvestmentCapitalUSD) 
    : 0;

  return {
    totalInvestmentCapitalUSD,
    totalPnLUSD,
    totalPnLPercent,
    openPositionsCount: openPositions.length
  };
};

// Test data examples
const testPositions: Position[] = [
  {
    id: 1,
    symbol: 'AAPL',
    position: 100,
    side: 'BUY',
    status: 'OPEN',
    tradePrice: 150.00,
    tradeValue: 15000,
    pnlValue: 500.00,
    pnlPercent: 0.0333,
    createdDate: '2024-01-01T00:00:00Z',
    updatedDate: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    symbol: '0700.HK', // Hong Kong stock
    position: 200,
    side: 'BUY',
    status: 'OPEN',
    tradePrice: 390.00, // HKD
    tradeValue: 78000, // HKD
    pnlValue: -3900.00, // HKD (loss)
    pnlPercent: -0.05,
    createdDate: '2024-01-01T00:00:00Z',
    updatedDate: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    symbol: 'TSLA',
    position: 50,
    side: 'BUY',
    status: 'CLOSED', // This should be excluded from summary
    tradePrice: 200.00,
    tradeValue: 10000,
    pnlValue: 1000.00,
    pnlPercent: 0.10,
    createdDate: '2024-01-01T00:00:00Z',
    updatedDate: '2024-01-01T00:00:00Z'
  }
];

// Test the calculations
export const runPortfolioSummaryTest = () => {
  console.log('=== Portfolio Summary Test ===');
  
  const summary = calculatePortfolioSummary(testPositions);
  
  console.log('Test Positions:');
  testPositions.forEach(pos => {
    console.log(`- ${pos.symbol}: ${pos.position} shares @ ${pos.tradePrice} (${pos.status})`);
    if (pos.symbol.endsWith('.HK')) {
      console.log(`  Investment Capital: ${pos.position * pos.tradePrice} HKD = ${convertToUSD(pos.position * pos.tradePrice, pos.symbol).toFixed(2)} USD`);
      console.log(`  P&L: ${pos.pnlValue} HKD = ${convertToUSD(pos.pnlValue || 0, pos.symbol).toFixed(2)} USD`);
    } else {
      console.log(`  Investment Capital: ${pos.position * pos.tradePrice} USD`);
      console.log(`  P&L: ${pos.pnlValue} USD`);
    }
  });
  
  console.log('\nCalculated Summary:');
  console.log(`- Open Positions Count: ${summary.openPositionsCount}`);
  console.log(`- Total Investment Capital (USD): $${summary.totalInvestmentCapitalUSD.toFixed(2)}`);
  console.log(`- Total P&L (USD): $${summary.totalPnLUSD.toFixed(2)}`);
  console.log(`- Total P&L Percentage: ${(summary.totalPnLPercent * 100).toFixed(2)}%`);
  
  // Expected calculations:
  // AAPL: 100 * 150 = $15,000 USD investment, $500 USD P&L
  // 0700.HK: 200 * 390 = 78,000 HKD = $10,000 USD investment, -3,900 HKD = -$500 USD P&L
  // TSLA: CLOSED - excluded
  // Total: $25,000 USD investment, $0 USD P&L, 0% P&L percentage
  
  console.log('\nExpected Results:');
  console.log('- Open Positions Count: 2');
  console.log('- Total Investment Capital (USD): $25,000.00');
  console.log('- Total P&L (USD): $0.00');
  console.log('- Total P&L Percentage: 0.00%');
  
  return summary;
};

export { convertToUSD, calculatePortfolioSummary };
