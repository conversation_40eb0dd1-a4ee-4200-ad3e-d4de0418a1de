import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  ToggleButtonGroup,
  ToggleButton,
  useTheme,
  alpha,
  Fade,
  Grow,
} from '@mui/material';
import {
  TrendingUp as StandardIcon,
  Psychology as EnhancedIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';

export type StopLossMode = 'STANDARD' | 'ENHANCED';

interface StopLossModeSelectorProps {
  value: StopLossMode;
  onChange: (mode: StopLossMode) => void;
  disabled?: boolean;
  showDescription?: boolean;
  variant?: 'default' | 'compact' | 'card';
}

const StopLossModeSelector: React.FC<StopLossModeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  showDescription = true,
  variant = 'default',
}) => {
  const theme = useTheme();

  const handleChange = (
    event: React.MouseEvent<HTMLElement>,
    newValue: StopLossMode | null,
  ) => {
    if (newValue !== null) {
      onChange(newValue);
    }
  };

  const isCompact = variant === 'compact';
  const isCard = variant === 'card';

  // Card-based design for enhanced visual appeal
  if (isCard) {
    return (
      <Box>
        <Typography
          variant="h6"
          gutterBottom
          sx={{
            mb: 3,
            fontWeight: 600,
            color: 'text.primary',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <StandardIcon color="primary" />
          Stop-Loss Calculation Mode
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', md: 'row' } }}>
          {/* Standard Mode Card */}
          <Card
            sx={{
              flex: 1,
              cursor: disabled ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              border: `2px solid ${value === 'STANDARD' ? theme.palette.primary.main : 'transparent'}`,
              backgroundColor: value === 'STANDARD'
                ? alpha(theme.palette.primary.main, 0.08)
                : 'background.paper',
              transform: value === 'STANDARD' ? 'translateY(-2px)' : 'none',
              boxShadow: value === 'STANDARD'
                ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.15)}`
                : theme.shadows[2],
              '&:hover': disabled ? {} : {
                transform: 'translateY(-4px)',
                boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.2)}`,
                borderColor: theme.palette.primary.main,
              },
              opacity: disabled ? 0.6 : 1,
            }}
            onClick={() => !disabled && onChange('STANDARD')}
          >
            <CardContent sx={{ p: 3, position: 'relative' }}>
              {value === 'STANDARD' && (
                <Fade in={true}>
                  <CheckIcon
                    sx={{
                      position: 'absolute',
                      top: 12,
                      right: 12,
                      color: 'primary.main',
                      fontSize: 24,
                    }}
                  />
                </Fade>
              )}

              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                <StandardIcon
                  sx={{
                    color: value === 'STANDARD' ? 'primary.main' : 'text.secondary',
                    fontSize: 32,
                    mt: 0.5
                  }}
                />
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: value === 'STANDARD' ? 'primary.main' : 'text.primary',
                      mb: 0.5
                    }}
                  >
                    Standard Mode
                  </Typography>
                  <Chip
                    label="Default"
                    size="small"
                    color="primary"
                    variant={value === 'STANDARD' ? 'filled' : 'outlined'}
                    sx={{
                      fontSize: '0.75rem',
                      height: 24
                    }}
                  />
                </Box>
              </Box>

              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ lineHeight: 1.6 }}
              >
                Basic stop-loss calculation using fixed parameters. Effective stop value
                is determined by the higher of trailing stop and Bollinger Band middle band stop.
              </Typography>
            </CardContent>
          </Card>

          {/* Enhanced Mode Card */}
          <Card
            sx={{
              flex: 1,
              cursor: disabled ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              border: `2px solid ${value === 'ENHANCED' ? theme.palette.secondary.main : 'transparent'}`,
              backgroundColor: value === 'ENHANCED'
                ? alpha(theme.palette.secondary.main, 0.08)
                : 'background.paper',
              transform: value === 'ENHANCED' ? 'translateY(-2px)' : 'none',
              boxShadow: value === 'ENHANCED'
                ? `0 8px 24px ${alpha(theme.palette.secondary.main, 0.15)}`
                : theme.shadows[2],
              '&:hover': disabled ? {} : {
                transform: 'translateY(-4px)',
                boxShadow: `0 12px 32px ${alpha(theme.palette.secondary.main, 0.2)}`,
                borderColor: theme.palette.secondary.main,
              },
              opacity: disabled ? 0.6 : 1,
            }}
            onClick={() => !disabled && onChange('ENHANCED')}
          >
            <CardContent sx={{ p: 3, position: 'relative' }}>
              {value === 'ENHANCED' && (
                <Fade in={true}>
                  <CheckIcon
                    sx={{
                      position: 'absolute',
                      top: 12,
                      right: 12,
                      color: 'secondary.main',
                      fontSize: 24,
                    }}
                  />
                </Fade>
              )}

              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                <EnhancedIcon
                  sx={{
                    color: value === 'ENHANCED' ? 'secondary.main' : 'text.secondary',
                    fontSize: 32,
                    mt: 0.5
                  }}
                />
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: value === 'ENHANCED' ? 'secondary.main' : 'text.primary',
                      mb: 0.5
                    }}
                  >
                    Enhanced Mode
                  </Typography>
                  <Tooltip title="Requires aggressive and conservative stop percentages to be configured">
                    <Chip
                      label="Advanced"
                      size="small"
                      color="secondary"
                      variant={value === 'ENHANCED' ? 'filled' : 'outlined'}
                      icon={<InfoIcon fontSize="small" />}
                      sx={{
                        fontSize: '0.75rem',
                        height: 24
                      }}
                    />
                  </Tooltip>
                </Box>
              </Box>

              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ lineHeight: 1.6 }}
              >
                Dynamic risk management with position age analysis and Bollinger Band trend detection.
                Automatically switches between aggressive and conservative approaches.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  }

  // Toggle button design for compact/default variants
  return (
    <Box>
      <Typography
        variant={isCompact ? "subtitle2" : "h6"}
        gutterBottom
        sx={{
          mb: isCompact ? 1 : 2,
          fontWeight: 600,
          color: 'text.primary'
        }}
      >
        Stop-Loss Calculation Mode
      </Typography>

      <ToggleButtonGroup
        value={value}
        exclusive
        onChange={handleChange}
        disabled={disabled}
        fullWidth
        sx={{
          '& .MuiToggleButton-root': {
            border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
            borderRadius: 2,
            py: isCompact ? 1 : 2,
            px: isCompact ? 2 : 3,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.04),
              borderColor: theme.palette.primary.main,
            },
            '&.Mui-selected': {
              backgroundColor: alpha(theme.palette.primary.main, 0.12),
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.16),
              },
            },
          },
        }}
      >
        <ToggleButton value="STANDARD" sx={{ flex: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, width: '100%' }}>
            <StandardIcon fontSize={isCompact ? "small" : "medium"} />
            <Box sx={{ textAlign: 'left', flex: 1 }}>
              <Typography variant={isCompact ? "body2" : "subtitle1"} fontWeight={600}>
                Standard Mode
              </Typography>
              {!isCompact && (
                <Typography variant="caption" color="text.secondary" display="block">
                  Basic stop-loss calculation using fixed parameters
                </Typography>
              )}
            </Box>
            <Chip
              label="Default"
              size="small"
              color="primary"
              variant={value === 'STANDARD' ? 'filled' : 'outlined'}
              sx={{ fontSize: '0.7rem' }}
            />
          </Box>
        </ToggleButton>

        <ToggleButton value="ENHANCED" sx={{ flex: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, width: '100%' }}>
            <EnhancedIcon fontSize={isCompact ? "small" : "medium"} />
            <Box sx={{ textAlign: 'left', flex: 1 }}>
              <Typography variant={isCompact ? "body2" : "subtitle1"} fontWeight={600}>
                Enhanced Mode
              </Typography>
              {!isCompact && (
                <Typography variant="caption" color="text.secondary" display="block">
                  Dynamic risk management with Bollinger Band analysis
                </Typography>
              )}
            </Box>
            <Tooltip title="Requires aggressive and conservative stop percentages to be configured">
              <Chip
                label="Advanced"
                size="small"
                color="secondary"
                variant={value === 'ENHANCED' ? 'filled' : 'outlined'}
                icon={<InfoIcon fontSize="small" />}
                sx={{ fontSize: '0.7rem' }}
              />
            </Tooltip>
          </Box>
        </ToggleButton>
      </ToggleButtonGroup>

      {showDescription && !isCompact && !isCard && (
        <Grow in={true} timeout={500}>
          <Box mt={3}>
            {value === 'STANDARD' && (
              <Alert
                severity="info"
                icon={<StandardIcon />}
                sx={{
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    color: 'primary.main',
                  },
                }}
              >
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  <strong>Standard Mode:</strong> Uses basic stop-loss calculations with fixed parameters.
                  Effective stop value is determined by the higher of trailing stop and Bollinger Band middle band stop.
                </Typography>
              </Alert>
            )}

            {value === 'ENHANCED' && (
              <Alert
                severity="info"
                icon={<EnhancedIcon />}
                sx={{
                  borderRadius: 2,
                  '& .MuiAlert-icon': {
                    color: 'secondary.main',
                  },
                }}
              >
                <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                  <strong>Enhanced Mode:</strong> Uses dynamic risk management with position age analysis and
                  Bollinger Band trend detection. Automatically switches between aggressive and conservative
                  approaches based on market conditions.
                </Typography>
                <Typography variant="body2" sx={{ mt: 1.5, lineHeight: 1.6 }}>
                  <strong>Requirements:</strong> Positions must have both aggressive and conservative stop
                  percentages configured. Falls back to standard mode if parameters are missing.
                </Typography>
              </Alert>
            )}
          </Box>
        </Grow>
      )}
    </Box>
  );
};

export default StopLossModeSelector;
