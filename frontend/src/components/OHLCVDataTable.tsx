import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  TablePagination,
  Chip,
  useTheme,
  useMediaQuery,
  Skeleton,
  Collapse,
  IconButton,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';

import { OHLCV } from '../types/api';

interface OHLCVDataTableProps {
  data: OHLCV[];
  loading: boolean;
  symbol: string;
}


const OHLCVDataTable: React.FC<OHLCVDataTableProps> = ({
  data,
  loading,
  symbol,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Pagination state
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(50);

  // Expanded rows state for mobile
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Sort data by date descending (most recent first)
  const sortedData = [...data].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Paginated data
  const paginatedData = sortedData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Utility functions
  const formatPrice = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatVolume = (value: number) => {
    if (value >= 1e9) {
      return `${(value / 1e9).toFixed(1)}B`;
    } else if (value >= 1e6) {
      return `${(value / 1e6).toFixed(1)}M`;
    } else if (value >= 1e3) {
      return `${(value / 1e3).toFixed(1)}K`;
    }
    return value.toLocaleString();
  };

  const formatIndicator = (value?: number) => {
    if (value === undefined || value === null) return 'N/A';
    return value.toFixed(2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const getPriceChangeIcon = (open: number, close: number) => {
    if (close > open) {
      return <TrendingUpIcon color="success" fontSize="small" />;
    } else if (close < open) {
      return <TrendingDownIcon color="error" fontSize="small" />;
    }
    return <RemoveIcon color="disabled" fontSize="small" />;
  };

  const getPriceChangeColor = (open: number, close: number) => {
    if (close > open) return 'success.main';
    if (close < open) return 'error.main';
    return 'text.secondary';
  };

  // Toggle expanded row for mobile
  const toggleExpandedRow = (date: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedRows(newExpanded);
  };

  // Handle pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Loading skeleton
  if (loading) {
    return (
      <Paper sx={{ mt: 3 }}>
        <Box p={2}>
          <Typography variant="h6" gutterBottom>
            Historical OHLCV Data
          </Typography>
          {[...Array(5)].map((_, index) => (
            <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 1 }} />
          ))}
        </Box>
      </Paper>
    );
  }

  // Empty data state
  if (!data || data.length === 0) {
    return (
      <Paper sx={{ mt: 3 }}>
        <Box p={4} textAlign="center">
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No Historical Data Available
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {symbol ? `No OHLCV data found for ${symbol}` : 'Select a symbol to view historical data'}
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper sx={{ mt: 3 }}>
      <Box p={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Historical OHLCV Data
          </Typography>
          <Chip
            label={`${data.length} records`}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>

        <TableContainer sx={{ maxHeight: 600 }}>
          <Table stickyHeader size={isMobile ? 'small' : 'medium'}>
            <TableHead>
              <TableRow>
                {isMobile && <TableCell><strong>Details</strong></TableCell>}
                <TableCell><strong>Date</strong></TableCell>
                <TableCell align="right"><strong>Open</strong></TableCell>
                <TableCell align="right"><strong>High</strong></TableCell>
                <TableCell align="right"><strong>Low</strong></TableCell>
                <TableCell align="right"><strong>Close</strong></TableCell>
                <TableCell align="right"><strong>Volume</strong></TableCell>
                {!isMobile && (
                  <>
                    <TableCell align="right"><strong>BB Upper</strong></TableCell>
                    <TableCell align="right"><strong>BB Middle</strong></TableCell>
                    <TableCell align="right"><strong>BB Lower</strong></TableCell>
                    <TableCell align="right"><strong>DMI +DI</strong></TableCell>
                    <TableCell align="right"><strong>DMI -DI</strong></TableCell>
                    <TableCell align="right"><strong>DMI DX</strong></TableCell>
                    <TableCell align="right"><strong>DMI ADX</strong></TableCell>
                  </>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedData.map((row, index) => (
                <React.Fragment key={`${row.date}-${index}`}>
                  <TableRow hover>
                    {isMobile && (
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => toggleExpandedRow(row.date)}
                        >
                          {expandedRows.has(row.date) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      </TableCell>
                    )}
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getPriceChangeIcon(row.open, row.close)}
                        {formatDate(row.date)}
                      </Box>
                    </TableCell>
                    <TableCell align="right">{formatPrice(row.open)}</TableCell>
                    <TableCell align="right">{formatPrice(row.high)}</TableCell>
                    <TableCell align="right">{formatPrice(row.low)}</TableCell>
                    <TableCell
                      align="right"
                      sx={{ color: getPriceChangeColor(row.open, row.close), fontWeight: 600 }}
                    >
                      {formatPrice(row.close)}
                    </TableCell>
                    <TableCell align="right">{formatVolume(row.volume)}</TableCell>
                    {!isMobile && (
                      <>
                        <TableCell align="right">{formatIndicator(row.bollingerUpper)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.bollingerMiddle)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.bollingerLower)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.dmiPlusDi)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.dmiMinusDi)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.dmiDx)}</TableCell>
                        <TableCell align="right">{formatIndicator(row.dmiAdx)}</TableCell>
                      </>
                    )}
                  </TableRow>
                  {isMobile && (
                    <TableRow>
                      <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
                        <Collapse in={expandedRows.has(row.date)} timeout="auto" unmountOnExit>
                          <Box sx={{ py: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Technical Indicators
                            </Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={6}>
                                <Card variant="outlined" sx={{ p: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    Bollinger Bands
                                  </Typography>
                                  <Typography variant="body2">
                                    Upper: {formatIndicator(row.bollingerUpper)}
                                  </Typography>
                                  <Typography variant="body2">
                                    Middle: {formatIndicator(row.bollingerMiddle)}
                                  </Typography>
                                  <Typography variant="body2">
                                    Lower: {formatIndicator(row.bollingerLower)}
                                  </Typography>
                                </Card>
                              </Grid>
                              <Grid item xs={6}>
                                <Card variant="outlined" sx={{ p: 1 }}>
                                  <Typography variant="caption" color="text.secondary">
                                    DMI Indicators
                                  </Typography>
                                  <Typography variant="body2">
                                    +DI: {formatIndicator(row.dmiPlusDi)}
                                  </Typography>
                                  <Typography variant="body2">
                                    -DI: {formatIndicator(row.dmiMinusDi)}
                                  </Typography>
                                  <Typography variant="body2">
                                    DX: {formatIndicator(row.dmiDx)}
                                  </Typography>
                                  <Typography variant="body2">
                                    ADX: {formatIndicator(row.dmiAdx)}
                                  </Typography>
                                </Card>
                              </Grid>
                            </Grid>
                          </Box>
                        </Collapse>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100, 125, 150, 175, 200]}
          component="div"
          count={sortedData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Rows per page:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
          }
        />
      </Box>
    </Paper>
  );
};

export default OHLCVDataTable;
