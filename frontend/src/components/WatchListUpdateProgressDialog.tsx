import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Sync as SyncIcon,
  HourglassEmpty as HourglassEmptyIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  Calculate as CalculateIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { WatchListUpdateResult, BulkUpdateProgress, BulkUpdateResult } from '../types/api';

interface WatchListUpdateProgressDialogProps {
  open: boolean;
  onClose: () => void;
  onCancel?: () => void;
  onRetryFailed?: () => void;
  isComplete: boolean;
  result?: WatchListUpdateResult;
  // New props for real-time progress
  progress?: BulkUpdateProgress[];
  bulkResult?: BulkUpdateResult;
  title?: string;
  startTime?: Date;
}

const WatchListUpdateProgressDialog: React.FC<WatchListUpdateProgressDialogProps> = ({
  open,
  onClose,
  onCancel,
  onRetryFailed,
  isComplete,
  result,
  progress,
  bulkResult,
  title = 'Update OHLCV Data',
  startTime,
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  // Determine if we're using the new bulk progress format or the old result format
  const isUsingBulkProgress = progress !== undefined || bulkResult !== undefined;
  const currentProgress = progress || [];
  const currentResult = bulkResult || result;

  // Update elapsed time every second
  useEffect(() => {
    if (!startTime || isComplete) return;

    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime, isComplete]);

  // Helper function to get timeout warning message
  const getTimeoutWarning = () => {
    if (elapsedTime > 2100) { // 35 minutes
      return {
        severity: 'error' as const,
        message: 'Operation is taking longer than expected (35+ minutes). This may indicate a connection timeout risk.'
      };
    } else if (elapsedTime > 1800) { // 30 minutes
      return {
        severity: 'warning' as const,
        message: 'Long-running operation in progress (30+ minutes). DMI calculations can take significant time.'
      };
    } else if (elapsedTime > 600) { // 10 minutes
      return {
        severity: 'info' as const,
        message: 'Processing is taking longer than usual. Please be patient as technical indicators are being calculated.'
      };
    }
    return null;
  };

  const formatElapsedTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'ohlcv':
        return <TimelineIcon color="primary" />;
      case 'bollinger':
        return <TrendingUpIcon color="secondary" />;
      case 'dmi':
        return <CalculateIcon color="info" />;
      case 'complete':
        return <CheckCircleIcon color="success" />;
      default:
        return <HourglassEmptyIcon color="disabled" />;
    }
  };

  const getPhaseDescription = (phase: string) => {
    switch (phase) {
      case 'ohlcv':
        return 'Updating OHLCV historical data from Yahoo Finance';
      case 'bollinger':
        return 'Recalculating Bollinger Bands indicators';
      case 'dmi':
        return 'Recalculating DMI technical indicators';
      case 'complete':
        return 'Update process completed successfully';
      default:
        return 'Preparing update process';
    }
  };

  // Helper functions for bulk progress format
  const getStatusIcon = (status: BulkUpdateProgress['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'processing':
        return <SyncIcon color="primary" sx={{ animation: 'spin 1s linear infinite' }} />;
      case 'pending':
      default:
        return <HourglassEmptyIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: BulkUpdateProgress['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'processing':
        return 'primary';
      case 'pending':
      default:
        return 'default';
    }
  };

  const calculateProgress = (): number => {
    if (isUsingBulkProgress) {
      // New bulk progress format
      if (currentProgress.length === 0) return 0;
      if (isComplete) return 100;

      const completedCount = currentProgress.filter(p => p.status === 'success' || p.status === 'error').length;
      return Math.round((completedCount / currentProgress.length) * 100);
    } else {
      // Old result format
      if (!result) return 0;
      if (isComplete) return 100;

      // Estimate progress based on phases completed
      const phases = ['ohlcv', 'bollinger', 'dmi'];
      let completedPhases = 0;

      if (result.ohlcvSuccessCount > 0 || result.ohlcvErrorCount > 0) completedPhases++;
      if (result.bollingerBandsProcessed > 0) completedPhases++;
      if (result.dmiProcessed > 0) completedPhases++;

      return Math.round((completedPhases / phases.length) * 100);
    }
  };

  const getCurrentSymbol = (): string | null => {
    if (!isUsingBulkProgress) return null;
    const processingSymbol = currentProgress.find(p => p.status === 'processing');
    return processingSymbol?.symbol || null;
  };

  const getProgressCounts = () => {
    if (isUsingBulkProgress) {
      const total = currentProgress.length;
      const completed = currentProgress.filter(p => p.status === 'success' || p.status === 'error').length;
      const successful = currentProgress.filter(p => p.status === 'success').length;
      const failed = currentProgress.filter(p => p.status === 'error').length;
      return { total, completed, successful, failed };
    } else if (result) {
      return {
        total: result.totalSymbols,
        completed: result.ohlcvSuccessCount + result.ohlcvErrorCount,
        successful: result.ohlcvSuccessCount,
        failed: result.ohlcvErrorCount
      };
    }
    return { total: 0, completed: 0, successful: 0, failed: 0 };
  };

  const hasFailedSymbols = (): boolean => {
    if (isUsingBulkProgress) {
      return currentProgress.some(p => p.status === 'error');
    }
    return result ? result.ohlcvErrorCount > 0 : false;
  };

  return (
    <Dialog
      open={open}
      onClose={isComplete ? onClose : undefined}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={!isComplete}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">{title}</Typography>
          {!isComplete && startTime && (
            <Chip
              label={`Elapsed: ${formatElapsedTime(elapsedTime)}`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      </DialogTitle>

      <DialogContent>
        {!isComplete && (
          <Box mb={3}>
            <Box display="flex" alignItems="center" mb={1}>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                {isUsingBulkProgress ? (
                  <>
                    Processing watch list symbols...
                    {getCurrentSymbol() && (
                      <Typography component="span" sx={{ ml: 1, fontWeight: 'bold' }}>
                        ({getCurrentSymbol()})
                      </Typography>
                    )}
                  </>
                ) : (
                  'Processing watch list symbols...'
                )}
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={calculateProgress()}
              sx={{ height: 8, borderRadius: 4 }}
            />
            <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
              <Typography variant="caption" color="text.secondary">
                {calculateProgress()}% complete
              </Typography>
              {isUsingBulkProgress && (
                <Typography variant="caption" color="text.secondary">
                  {(() => {
                    const counts = getProgressCounts();
                    return `${counts.completed} of ${counts.total} symbols`;
                  })()}
                </Typography>
              )}
            </Box>

            {/* Timeout Warning */}
            {(() => {
              const warning = getTimeoutWarning();
              return warning ? (
                <Alert severity={warning.severity} sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    {warning.message}
                  </Typography>
                </Alert>
              ) : null;
            })()}

            {/* Processing Info */}
            {!isUsingBulkProgress && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block', textAlign: 'center' }}>
                Processing phases: OHLCV Data → Bollinger Bands → DMI Indicators
                <br />
                DMI calculations require significant processing time (up to 30+ minutes)
              </Typography>
            )}
          </Box>
        )}

        {(currentResult || currentProgress.length > 0) && (
          <Box>
            {/* Summary Statistics */}
            <Box mb={2}>
              <Typography variant="subtitle2" gutterBottom>
                Update Summary
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {(() => {
                  const counts = getProgressCounts();
                  return (
                    <>
                      <Chip
                        label={`${counts.total} symbols`}
                        size="small"
                        color="primary"
                      />
                      <Chip
                        label={`${counts.successful} successful`}
                        size="small"
                        color="success"
                      />
                      {counts.failed > 0 && (
                        <Chip
                          label={`${counts.failed} errors`}
                          size="small"
                          color="error"
                        />
                      )}
                      {!isUsingBulkProgress && result && (
                        <Chip
                          label={`${result.totalRecordsUpdated} records`}
                          size="small"
                          color="info"
                        />
                      )}
                    </>
                  );
                })()}
              </Box>
            </Box>

            {/* Processing Phases - Only show for old format */}
            {!isUsingBulkProgress && result && (
              <Box mb={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Processing Phases
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>{getPhaseIcon('ohlcv')}</ListItemIcon>
                    <ListItemText
                      primary="OHLCV Data Update"
                      secondary={`${result.ohlcvSuccessCount || 0} successful, ${result.ohlcvErrorCount || 0} failed`}
                    />
                    {((result.ohlcvSuccessCount || 0) > 0 || (result.ohlcvErrorCount || 0) > 0) && (
                      <CheckCircleIcon color="success" />
                    )}
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>{getPhaseIcon('bollinger')}</ListItemIcon>
                    <ListItemText
                      primary="Bollinger Bands Calculation"
                      secondary={`${result.bollingerBandsProcessed || 0} symbols processed`}
                    />
                    {(result.bollingerBandsProcessed || 0) > 0 && (
                      <CheckCircleIcon color="success" />
                    )}
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>{getPhaseIcon('dmi')}</ListItemIcon>
                    <ListItemText
                      primary="DMI Indicators Calculation"
                      secondary={`${result.dmiProcessed || 0} symbols processed`}
                    />
                    {(result.dmiProcessed || 0) > 0 && (
                      <CheckCircleIcon color="success" />
                    )}
                  </ListItem>
                </List>
              </Box>
            )}

            {/* Detailed Results */}
            {((isUsingBulkProgress && currentProgress.length > 0) ||
              (!isUsingBulkProgress && result?.phases?.ohlcv && result.phases.ohlcv.length > 0)) && (
              <Box>
                <Button
                  startIcon={showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  onClick={() => setShowDetails(!showDetails)}
                  size="small"
                >
                  {showDetails ? 'Hide' : 'Show'} Symbol Details
                </Button>

                <Collapse in={showDetails}>
                  <Box mt={2} maxHeight={300} overflow="auto">
                    <List dense>
                      {isUsingBulkProgress ? (
                        // New bulk progress format
                        currentProgress.map((item, index) => (
                          <React.Fragment key={item.symbol}>
                            <ListItem>
                              <ListItemIcon>
                                {getStatusIcon(item.status)}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" fontWeight={600}>
                                      {item.symbol}
                                    </Typography>
                                    <Chip
                                      label={item.status}
                                      size="small"
                                      color={getStatusColor(item.status) as any}
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={
                                  item.status === 'error' ? item.error : item.message
                                }
                                secondaryTypographyProps={{
                                  color: item.status === 'error' ? 'error' : 'text.secondary',
                                  variant: 'body2',
                                }}
                              />
                            </ListItem>
                            {index < currentProgress.length - 1 && <Divider />}
                          </React.Fragment>
                        ))
                      ) : (
                        // Old result format
                        result?.phases?.ohlcv?.map((item, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              {item.status === 'success' && <CheckCircleIcon color="success" />}
                              {item.status === 'error' && <ErrorIcon color="error" />}
                              {item.status === 'processing' && <SyncIcon color="primary" />}
                              {item.status === 'pending' && <HourglassEmptyIcon color="disabled" />}
                            </ListItemIcon>
                            <ListItemText
                              primary={item.symbol}
                              secondary={item.message || item.error}
                            />
                          </ListItem>
                        ))
                      )}
                    </List>
                  </Box>
                </Collapse>
              </Box>
            )}

            {/* Completion Message */}
            {isComplete && (
              <Alert
                severity={(() => {
                  const counts = getProgressCounts();
                  return counts.failed === 0 ? "success" : counts.successful === 0 ? "error" : "warning";
                })()}
                sx={{ mt: 2 }}
              >
                <Typography variant="body2">
                  {isUsingBulkProgress && bulkResult ? bulkResult.summary : result?.summary}
                </Typography>
                {!isUsingBulkProgress && result?.processingTimeMs && (
                  <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                    Processing time: {Math.round(result.processingTimeMs / 1000)} seconds
                  </Typography>
                )}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        {!isComplete && onCancel && (
          <Button onClick={onCancel} color="secondary">
            Cancel
          </Button>
        )}
        {isComplete && hasFailedSymbols() && onRetryFailed && (
          <Button
            onClick={onRetryFailed}
            startIcon={<RefreshIcon />}
            color="warning"
            variant="outlined"
          >
            Retry Failed
          </Button>
        )}
        <Button
          onClick={onClose}
          variant={isComplete ? "contained" : "outlined"}
          disabled={!isComplete}
        >
          {isComplete ? 'Close' : 'Processing...'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default WatchListUpdateProgressDialog;
